# MarkEraser 项目开发规则

## 项目背景
这是一个短视频去水印工具项目，支持抖音、快手、小红书、B站、微博等平台的视频解析和下载。

## AI助手行为规则

### 必须执行的规则
1. **每次回答后都必须更新 `开发进度记录.md` 文件**
   - 更新当前开发状态
   - 记录已完成的工作
   - 更新待解决问题
   - 更新最后修改时间

2. **保持开发进度记录的完整性**
   - 详细记录技术背景和实现细节
   - 包含关键代码片段和文件路径
   - 记录已尝试的解决方案和结果
   - 明确标记下一步计划

3. **回答风格**
   - 技术细节要详细准确
   - 提供可执行的操作步骤
   - 包含相关代码示例
   - 说明潜在的风险和注意事项

### 当前开发焦点
- **主要问题**：微博视频代理服务配置
- **关键文件**：proxy-video云函数、components/config.json
- **技术栈**：uni-app + uniCloud + 微信小程序

### 项目结构说明
- `uniCloud-aliyun/cloudfunctions/` - 云函数目录
- `components/config.json` - 配置文件
- `pages/result/index.vue` - 结果页面（包含代理逻辑）
- `开发进度记录.md` - 开发进度跟踪文件

### 开发环境
- 操作系统：macOS
- 开发工具：HBuilder X + 微信开发工具
- 云服务：uniCloud 阿里云
