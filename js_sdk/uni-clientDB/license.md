# clientDB使用许可协议
本协议是数字天堂（北京）网络技术有限公司（以下称“DCloud”）与您之间达成的关于clientDB框架（以下简称本框架）的协议。
本协议签订地点为中华人民共和国北京市海淀区。
您使用本框架即视为您已阅读并同意受本协议的约束。

## 知识产权及使用授权
您可以自由下载、使用、复制本框架而不需要向DCloud付费。
DCloud所拥有的知识产权，包括但不限于商标、专利、著作权、商业秘密、专有数据、源码，并不发生转移或共享。
您使用本框架开发的代码及输出物，包括但不限于网站、移动应用，其知识产权归属您所有。
本框架未包含第三方软件或技术，不涉及额外遵循第三方软件的授权协议问题。

## 您的义务
您不得破解、反编译、逆向工程本框架，不得破解或劫持本框架网络请求，不得对DCloud服务进行网络攻击，不得利用DCloud系统漏洞谋利或侵害DCloud利益，不得替换、删改本框架自带的非用户自定义文件。
未经书面许可您不可利用DCloud产品的全部或部分文件、模块、组件来制作与DCloud争夺用户的产品（通过DCloud插件市场服务开发者不属于此范围）。
如果您违反您的义务，DCloud将有权停止您使用本框架，造成的损失由您自行承担。
如果您给DCloud造成重大损失，或者在接收到DCloud的停止违约通知后拒不改正，DCloud将有权停止对您的DCloud所有产品和服务的使用授权，冻结您在DCloud所有产品服务中的预付款项和应收款项，因此造成的损失由您自行承担。
如果您的行为产生法律问题，DCloud有权追责您的法律责任。

## 隐私条款
本框架未进行任何数据采集、发送等涉及数据隐私的行为。

## 安全
您理解并同意，本框架同其他软件一样，无法承诺绝对的安全性。
当DCloud发现本框架的任何安全漏洞时，将及时在[社区](https://ask.dcloud.net.cn/explore/)发送公告，并将及时发布紧急更新补丁和升级推送通知。

## 免责声明
DCloud不因开发者使用本框架而承担任何法律责任。

## 协议修订
根据发展，DCloud可能会对本协议进行修改。修改时，DCloud会在产品或者网页中显著的位置发布相关信息以便及时通知到用户。如果您选择继续使用本框架，即表示您同意接受这些修改。
