<template>
  <view class="container">
    <!-- 顶部横幅广告已移除 -->
    
    <!-- 顶部信息 -->
    <ResultHeader 
      :statusBarHeight="statusBarHeight" 
      :customNavHeight="customNavHeight"
      @back="goBack" 
    />

    <!-- 图片选择器 -->
    <image-selector
      :show="showImageSelector"
      :images="selectorImages"
      @confirm="onImageSelectorConfirm"
      @cancel="onImageSelectorCancel"
    />

    <!-- Live Photo 选择器 -->
    <live-photo-selector
      :show="showLivePhotoSelector"
      :videoUrls="livePhotoSelectorVideos"
      :imageUrls="livePhotoSelectorImages"
      :livePhotoVideos="livePhotoSelectorLiveVideos"
      @confirm="onLivePhotoSelectorConfirm"
      @cancel="onLivePhotoSelectorCancel"
    />
    
    <!-- 操作前广告组件 -->
    <OperationAd @complete="onOperationAdComplete" />

    <!-- 分享弹窗组件 -->
    <ShareDialog />



    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: customNavHeight + 'px' }">
      <!-- 常驻广告位 - 移到最上面 -->
      <view class="result-ad-placeholder" v-if="shouldShowResultAd">
        <view class="result-ad-header">
          <text class="result-ad-label">📺 智能解析结果</text>
          <text class="result-ad-close" @click="closeResultAd">✕</text>
        </view>
        <view class="result-ad-content">
          <view class="result-ad-thumbnail">
            <text class="result-ad-icon">🎯</text>
          </view>
          <view class="result-ad-info">
            <text class="result-ad-title">AI智能解析</text>
            <text class="result-ad-desc">深度学习算法，精准提取无水印内容</text>
            <view class="result-ad-source">
              <text class="result-ad-source-icon">🔬</text>
              <text class="result-ad-source-text">墨影去水印</text>
            </view>
          </view>
          <button class="result-ad-action-btn" @click="onResultAdClick">立即体验</button>
        </view>
      </view>

      <!-- 视频信息 -->
      <ResultVideoInfo 
        :title="resultData.title"
        :content="resultData.content"
        :author="resultData.author"
        :source="resultData.source"
        :type="resultData.type"
        :platform="resultData.platform"
      />

    <!-- 视频播放区域 -->
    <view class="video-section">
      <view class="video-container">
        <!-- 大文件警告（基于实际文件大小） -->
        <view class="large-file-warning" v-if="shouldShowLargeFileWarning">
          <view class="warning-header">
            <text class="warning-icon">⚠️</text>
            <text class="warning-title">大文件提醒</text>
          </view>
          <view class="warning-content">
            <text class="warning-text">文件较大（{{estimatedFileSize}}），建议在WiFi环境下使用。</text>
            <text class="warning-suggestion">如下载失败，可复制链接到浏览器下载。</text>
          </view>
        </view>


        <!-- 处理中状态 -->
        <view v-if="resultData.processedData && resultData.processedData.isProcessing" class="processing-placeholder">
          <view class="processing-content">
            <view class="loading-spinner"></view>
            <text class="processing-text">正在后台处理视频...</text>
            <text class="processing-tip">请稍候，处理完成后将自动播放</text>
          </view>
        </view>

        <!-- 纯文本内容显示 -->
        <ResultTextContent 
          v-if="resultData.type === 'text'"
          :content="resultData.content || resultData.description"
          :source="resultData.source"
          :platform="resultData.platform"
        />

        <!-- 图文内容显示 -->
        <view v-else-if="resultData.type === 'image'" class="image-content">
          <view class="main-image-container">
            <!-- 图片加载状态 -->
            <view v-if="imageLoading" class="image-loading-placeholder">
              <view class="loading-spinner-small"></view>
              <text class="loading-text-small">加载中...</text>
            </view>

            <image
              :src="getCurrentImageUrl()"
              mode="aspectFit"
              class="main-image"
              :class="{ 'image-hidden': imageLoading }"
              @error="onMainImageError"
              @load="onMainImageLoad"
              @click="previewCurrentImage"
            ></image>

            <!-- 主图上的Live Photo操作按钮 -->
            <view v-if="hasLivePhotoForImage(currentImageIndex)" class="main-live-photo-actions">
              <view class="main-live-photo-container">
                <view class="main-live-photo-btn"
                      @click="playLivePhotoForImage(currentImageIndex)">
                  <view class="main-play-icon">▶</view>
                  <text class="main-live-text">LIVE PHOTO</text>
                </view>
                <view class="main-live-copy-btn"
                      @click="copyLivePhotoUrl(currentImageIndex)">
                  <view class="main-copy-icon">📋</view>
                </view>
              </view>
            </view>

            <!-- 主图上的复制链接按钮 (仅图文内容且有图片链接时显示，微博平台除外) -->
            <view v-if="resultData.type === 'image' && resultData.platform !== 'weibo' && resultData.processedData && 
                        (resultData.processedData.isUrl || (resultData.processedData.imageUrls && resultData.processedData.imageUrls.length > 0))"
                  class="main-copy-link-btn"
                  @click="copyCurrentImageUrl">
              <view class="main-copy-icon">🔗</view>
              <text class="main-copy-text">复制链接</text>
            </view>

            <!-- 图片信息 -->
            <view class="main-image-info">
              <text class="image-counter">{{ currentImageIndex + 1 }} / {{ resultData.processedData.imageUrls?.length || 1 }}</text>
            </view>
          </view>

          <!-- 多图显示 -->
          <view v-if="resultData.processedData.imageUrls && resultData.processedData.imageUrls.length > 1"
                class="image-gallery">
            <text class="gallery-title">
              共{{ resultData.processedData.imageUrls.length }}张图片
              <text v-if="livePhotoCount > 0"
                    class="live-photo-count-inline">
                ({{ livePhotoCount }}个Live Photo)
              </text>
            </text>
            <scroll-view scroll-x class="gallery-scroll">
              <view class="gallery-item" v-for="(imageUrl, index) in resultData.processedData.imageUrls" :key="index">
                <view class="image-container" :class="{ 'active': currentImageIndex === index }">
                  <image
                    :src="imageUrl"
                    mode="aspectFill"
                    class="gallery-image"
                    @click="selectImage(index)"
                  ></image>

                  <!-- Live Photo 播放按钮 -->
                  <view v-if="hasLivePhotoForImage(index)"
                        class="live-photo-play-btn"
                        @click.stop="playLivePhotoForImage(index)">
                    <view class="play-icon-small">▶</view>
                    <text class="live-text">LIVE</text>
                  </view>

                  <!-- 图片序号 -->
                  <view class="image-index">{{ index + 1 }}</view>

                  <!-- 选中状态指示器 -->
                  <view v-if="currentImageIndex === index" class="selected-indicator">✓</view>
                </view>
              </view>
            </scroll-view>
          </view>

          <!-- 隐藏的Live Photo视频播放器 -->
          <view v-if="showLivePhotoPlayer" class="live-photo-player-modal" @click="closeLivePhotoPlayer">
            <view class="live-photo-player-container" @click.stop>
              <view class="live-photo-player-header">
                <text class="live-photo-player-title">Live Photo {{ currentLivePhotoIndex + 1 }}</text>
                <view class="live-photo-player-close" @click="closeLivePhotoPlayer">✕</view>
              </view>

              <video
                id="livePhotoPlayerVideo"
                :src="currentLivePhotoUrl"
                class="live-photo-player-video"
                :controls="true"
                :autoplay="true"
                :show-fullscreen-btn="true"
                :show-play-btn="true"
                :show-center-play-btn="true"
                @ended="onLivePhotoPlayerEnded"
              ></video>

              <view class="live-photo-player-actions">
                <button class="live-photo-player-btn" @click="downloadCurrentLivePhoto">
                  <text class="btn-icon">💾</text>
                  <text class="btn-text">保存到相册</text>
                </button>
                <button class="live-photo-player-btn secondary" @click="copyCurrentLivePhotoUrl">
                  <text class="btn-icon">📋</text>
                  <text class="btn-text">复制链接</text>
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 正常视频播放 -->
        <video
          v-else
          :src="getVideoSrc(resultData.processedData)"
          controls
          class="main-video"
          @error="onVideoError"
          @loadstart="onVideoLoadStart"
          @canplay="onVideoCanPlay"
          show-center-play-btn
          show-fullscreen-btn
          show-play-btn
        ></video>

        <view class="video-status" v-if="videoStatus">
          <text class="status-text">{{ videoStatus }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <!-- 纯文本内容的操作按钮 -->
      <view v-if="resultData.type === 'text'">
        <view class="action-row">
          <button class="action-btn primary-btn half-width" @click="getCopyText">
            <text class="btn-icon">📝</text>
            <text class="btn-text">复制文案</text>
          </button>
          <button v-if="shouldShowShareButton" class="action-btn share-btn half-width" @click="showShareDialog">
            <text class="btn-icon">📤</text>
            <text class="btn-text">{{ shareButtonText }}</text>
          </button>
        </view>
      </view>

      <!-- 非纯文本内容的操作按钮 -->
      <view v-else>
        <!-- 第一行：主要操作 -->
        <view class="action-row">
          <button class="action-btn primary-btn half-width" @click="saveToAlbum">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存到相册</text>
          </button>
          <button v-if="shouldShowShareButton" class="action-btn share-btn half-width" @click="showShareDialog">
            <text class="btn-icon">📤</text>
            <text class="btn-text">{{ shareButtonText }}</text>
          </button>
        </view>



        <!-- 第二行：辅助操作 -->
        <view class="action-row">
          <button class="action-btn secondary-btn half-width" @click="getCopyText">
            <text class="btn-icon">📝</text>
            <text class="btn-text">获取文案</text>
          </button>

          <button class="action-btn secondary-btn half-width" @click="getCover">
            <text class="btn-icon">🖼️</text>
            <text class="btn-text">获取封面</text>
          </button>
        </view>

        <!-- 第三行：链接操作 (多图时隐藏，因为主图上有复制链接按钮) -->
        <view v-if="shouldShowCopyLink" class="action-row">
          <button class="action-btn secondary-btn full-width" @click="copyVideoUrl">
            <text class="btn-icon">🔗</text>
            <text class="btn-text">复制链接</text>
          </button>
        </view>

        <!-- 第四行：帮助按钮 -->
        <view class="action-row">
          <button class="action-btn help-btn-new full-width" @click="goToTutorialDownloadFailed">
            <view class="help-icon-container">
              <text class="help-icon">💡</text>
              <view class="help-icon-shine"></view>
            </view>
            <view class="help-text-container">
              <text class="help-text">保存失败？查看解决方案</text>
            </view>
            <view class="help-arrow">
              <text class="help-arrow-text">查看</text>
            </view>
          </button>
        </view>

        <!-- 保存路径显示 -->
        <view v-if="savedPath" class="saved-path-container">
          <view class="saved-path-header">
            <text class="saved-path-icon">📁</text>
            <text class="saved-path-title">保存位置</text>
          </view>

          <!-- 用户友好的中文说明 -->
          <view class="saved-path-friendly">
            <text class="friendly-text">{{ friendlyPath }}</text>
          </view>

          <!-- 详细路径 -->
          <view class="saved-path-detail">
            <text class="detail-label">路径：</text>
            <text class="detail-path">{{ savedPath }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 处理信息 -->
    <view class="process-info">
      <!-- 只在纯视频内容时显示文件大小 -->
      <view v-if="resultData.type === 'video'" class="info-item">
        <text class="info-label">文件大小：</text>
        <text class="info-value">{{ getOptimizedDataSize(resultData.processedData) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">内容信息：</text>
        <text class="info-value">{{ getOptimizedNote(resultData) }}</text>
      </view>
    </view>

    <!-- 免责声明 -->
    <view class="disclaimer">
      <text class="disclaimer-title">⚠️ 重要提醒</text>
      <text class="disclaimer-text">
        1. 本工具仅供个人学习交流使用
        2. 请尊重原创作者的版权
        3. 禁止用于任何商业或侵权用途
        4. 视频版权归原作者所有
      </text>
    </view>
    </view> <!-- 关闭page-content -->

    <!-- 自定义Loading组件 -->
    <view v-if="customLoading.show" class="custom-loading-mask">
      <view class="custom-loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ customLoading.title }}</text>
      </view>
    </view>
    
    <!-- 自定义大文件提醒弹窗 -->
    <view v-if="showLargeFileDialogFlag" class="large-file-dialog-mask" @click="hideLargeFileDialog">
      <view class="large-file-dialog" @click.stop>
        <view class="dialog-header">
          <text class="dialog-icon">⚠️</text>
          <text class="dialog-title">大文件提醒</text>
        </view>
        
        <view class="dialog-content">
          <text class="dialog-main-text">文件较大（约{{largeFileSize}}MB）</text>
          <text class="dialog-sub-text">建议连接WiFi下载，是否继续？</text>
          <text class="dialog-tip-text">💡 如果下载失败，建议点击"复制链接"到浏览器下载</text>
        </view>
        
        <view class="dialog-actions">
          <button class="dialog-btn cancel-btn" @click="onLargeFileCancel">
            <text class="btn-text">复制链接</text>
          </button>
          <button class="dialog-btn confirm-btn" @click="onLargeFileConfirm">
            <text class="btn-text">继续下载</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 广告组件 -->

  </view>
</template>

<script>
import ImageSelector from '@/components/image-selector/image-selector-simple.vue'
import LivePhotoSelector from '@/components/live-photo-selector/live-photo-selector.vue'
import OperationAd from '../../components/operation-ad/operation-ad.vue'
import ShareDialog from '../../components/share-dialog/share-dialog.vue'
import ResultHeader from '../../components/result-header/result-header.vue'
import ResultVideoInfo from '../../components/result-video-info/result-video-info.vue'
import ResultTextContent from '../../components/result-text-content/result-text-content.vue'
// BannerAd 已移除
import adManager from '../../components/ad-config.js'
import shareManager from '../../components/share-config.js'

export default {
  components: {
    ImageSelector,
    LivePhotoSelector,
    OperationAd,
    ShareDialog,
    ResultHeader,
    ResultVideoInfo,
    ResultTextContent
  },

  data() {
    return {
      resultData: {},
      videoStatus: '',
      statusBarHeight: 0, // 状态栏高度
      customNavHeight: 0, // 自定义导航栏高度
      currentDownloadTask: null, // 当前下载任务
      isPageActive: true, // 页面是否活跃

      cachedFileSize: 0, // 缓存的文件大小
      loadingSizePromise: null, // 文件大小加载Promise，防止重复请求
      showImageSelector: false, // 显示图片选择器
      selectorImages: [], // 选择器中的图片列表
      currentImageSelectorResolve: null, // 当前图片选择器的resolve函数
      // Live Photo 选择器相关
      showLivePhotoSelector: false,
      livePhotoSelectorVideos: [],
      livePhotoSelectorImages: [],
      livePhotoSelectorLiveVideos: [],
      currentLivePhotoSelectorResolve: null,
      customLoading: {
        show: false,
        title: ''
      },
      // 大文件提醒弹窗
      showLargeFileDialogFlag: false,
      largeFileSize: 0,
      largeFileDialogResolve: null,
      // Live Photo 相关
      showLivePhotoPlayer: false,
      currentLivePhotoIndex: 0,
      currentLivePhotoUrl: '',
      // 图片预览相关
      currentImageIndex: 0,
      imageLoading: false,
      preloadedImages: new Set(), // 已预加载的图片集合
      // 保存路径显示
      savedPath: '', // 技术路径
      friendlyPath: '', // 用户友好的路径说明
      // 广告相关（将通过computed计算）
    }
  },
  
  computed: {
    // 🔧 检查是否应该显示结果页广告
    shouldShowResultAd() {
      return adManager.shouldShowAd('result')
    },

    // 计算实际Live Photo数量
    livePhotoCount() {
      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      if (!livePhotoVideos) return 0;
      return livePhotoVideos.filter(video => video && video !== null).length;
    },
    
    // 判断是否应该显示复制链接按钮
    shouldShowCopyLink() {
      // 如果是图文内容，不显示复制链接按钮（因为图片上已经有复制按钮了）
      if (this.resultData.type === 'image') {
        return false;
      }
      
      // 如果有可用的URL数据，显示复制链接按钮
      if (this.resultData.processedData && this.resultData.processedData.isUrl) {
        // 额外检查：如果是微博链接，不显示复制按钮（因为无法直接访问）
        const videoUrl = this.resultData.processedData.data || this.resultData.processedData.videoUrl;
        if (videoUrl && typeof videoUrl === 'string' && (videoUrl.includes('weibocdn.com') || videoUrl.includes('sinaimg.cn'))) {
          return false;
        }
        return true;
      }
      
      return false;
    },

    // 判断是否应该显示分享按钮
    shouldShowShareButton() {
      return shareManager.shouldShowShare('resultPage')
    },

    // 获取分享按钮配置
    shareButtonConfig() {
      return shareManager.getShareButtonConfig('resultPage')
    },

    // 分享按钮文字
    shareButtonText() {
      const config = this.shareButtonConfig
      return config.showRewardHint ? '分享小程序' : '分享小程序'
    },

    // 是否显示大文件警告
    shouldShowLargeFileWarning() {
      // 只对视频内容显示警告
      if (this.resultData?.type !== 'video') {
        return false
      }
      
      // 检查是否有缓存的文件大小信息
      if (this.cachedFileSize && this.cachedFileSize > 30 * 1024 * 1024) {
        return true
      }
      
      // 如果没有缓存信息，但有时长信息，可以基于时长估算
      const duration = this.resultData.processedData?.duration || 0
      const durationInSeconds = duration > 1000 ? Math.round(duration / 1000) : duration
      
      // 如果视频超过3分钟，很可能是大文件
      return durationInSeconds > 180
    },

    // 估算文件大小显示
    estimatedFileSize() {
      if (this.cachedFileSize) {
        return this.formatFileSize(this.cachedFileSize)
      }
      
      // 如果没有缓存信息，基于时长估算
      const duration = this.resultData.processedData?.duration || 0
      const durationInSeconds = duration > 1000 ? Math.round(duration / 1000) : duration
      
      if (durationInSeconds > 0) {
        const estimatedSize = this.estimateVideoSize(durationInSeconds)
        return `约 ${this.formatFileSize(estimatedSize)}`
      }
      
      return '较大'
    }
  },
  
  onLoad(options) {
    // 获取系统信息，适配刘海屏
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    this.customNavHeight = this.statusBarHeight + 44 // 44是导航栏内容高度



    // 从页面参数或全局数据中获取结果
    const resultStr = options.result || uni.getStorageSync('temp_result')
    if (resultStr) {
      try {
        this.resultData = JSON.parse(resultStr)

        // 如果是纯视频内容且为URL格式，尝试获取文件大小
        if (this.resultData.type === 'video' && this.resultData.processedData && this.resultData.processedData.isUrl) {
          this.loadFileSize()
        }

        // 如果是图文内容，开始预加载图片
        if (this.resultData.type === 'image' && this.resultData.processedData?.imageUrls?.length > 1) {
          this.$nextTick(() => {
            this.preloadAdjacentImages(0) // 预加载第一张图片周围的图片
          })
        }
      } catch (error) {
        console.error('解析结果数据失败:', error)
        this.goBack()
      }
    } else {
      uni.showToast({
        title: '没有找到结果数据',
        icon: 'none'
      })
      this.goBack()
    }
  },

  // 页面隐藏时中断下载
  onHide() {
    this.isPageActive = false
    this.cancelCurrentDownload()
    // 通知分享弹窗关闭
    uni.$emit('onHide')
    uni.$emit('onPageSwitch')
  },

  // 页面卸载时中断下载
  onUnload() {
    this.isPageActive = false
    this.cancelCurrentDownload()
    // 通知分享弹窗关闭
    uni.$emit('onPageSwitch')
  },

  // 页面显示时恢复状态
  onShow() {
    this.isPageActive = true

  },

  methods: {
    // 🔐 统一权限检查方法 - 简单可靠
    async checkOperationPermission(actionType) {
      console.log(`[权限检查] 开始检查: ${actionType}`)

      // 1. 检查24小时权限
      if (adManager.hasUnlimitedAccess()) {
        console.log(`[权限检查] 有24小时权限，允许操作`)
        return true
      }

      // 2. 显示广告（如果广告开启）
      if (adManager.config.globalEnabled && adManager.shouldShowAd('operation', actionType)) {
        console.log(`[权限检查] 显示广告`)
        const adResult = await adManager.showOperationAd(actionType)
        if (!adResult) {
          console.log(`[权限检查] 用户取消广告`)
          return false
        }
        console.log(`[权限检查] 广告完成`)
      }

      // 3. 检查分享权限（如果分享开启且需要分享）
      if (shareManager.config.globalEnabled && shareManager.shouldForceShare(actionType)) {
        const sharePermission = shareManager.checkActionPermission(actionType)
        if (!sharePermission.allowed) {
          console.log(`[权限检查] 需要分享权限`)
          return new Promise((resolve) => {
            shareManager.showForceShareDialog(actionType, (result) => {
              if (result.allowed) {
                console.log(`[权限检查] 分享成功`)
                resolve(true)
              } else {
                console.log(`[权限检查] 用户取消分享`)
                resolve(false)
              }
            })
          })
        }
      }

      console.log(`[权限检查] 权限检查通过`)
      return true
    },

    // 显示分享弹窗
    showShareDialog() {
      console.log('[分享] 用户点击分享按钮')
      shareManager.showShareDialog({
        entryType: 'resultPage',
        showRewardHint: true
      })
    },




    // 显示自定义loading
    showCustomLoading(title) {
      this.customLoading.show = true
      this.customLoading.title = title
    },

    // 隐藏自定义loading
    hideCustomLoading() {
      this.customLoading.show = false
      this.customLoading.title = ''
    },

    // 显示大文件提醒弹窗
    showLargeFileDialog(fileSizeMB) {
      return new Promise((resolve) => {
        this.largeFileSize = Math.round(fileSizeMB)
        this.largeFileDialogResolve = resolve
        this.showLargeFileDialogFlag = true
      })
    },

    // 隐藏大文件提醒弹窗
    hideLargeFileDialog() {
      this.showLargeFileDialogFlag = false
      if (this.largeFileDialogResolve) {
        this.largeFileDialogResolve(false)
        this.largeFileDialogResolve = null
      }
    },

    // 大文件弹窗 - 继续下载
    onLargeFileConfirm() {
      this.showLargeFileDialogFlag = false
      if (this.largeFileDialogResolve) {
        this.largeFileDialogResolve(true)
        this.largeFileDialogResolve = null
      }
    },

    // 大文件弹窗 - 复制链接
    onLargeFileCancel() {
      this.showLargeFileDialogFlag = false
      this.copyVideoUrl() // 复制链接
      if (this.largeFileDialogResolve) {
        this.largeFileDialogResolve(false)
        this.largeFileDialogResolve = null
      }
    },

    // 图片选择器确认回调
    onImageSelectorConfirm(selectedIndexes) {
      this.showImageSelector = false
      if (this.currentImageSelectorResolve) {
        this.currentImageSelectorResolve(selectedIndexes)
        this.currentImageSelectorResolve = null
      }
    },

    // 图片选择器取消回调
    onImageSelectorCancel() {
      this.showImageSelector = false
      if (this.currentImageSelectorResolve) {
        this.currentImageSelectorResolve(null)
        this.currentImageSelectorResolve = null
      }
    },

    // Live Photo 选择器确认回调
    onLivePhotoSelectorConfirm(selectedIndexes) {
      this.showLivePhotoSelector = false
      if (this.currentLivePhotoSelectorResolve) {
        this.currentLivePhotoSelectorResolve(selectedIndexes)
        this.currentLivePhotoSelectorResolve = null
      }
    },

    // Live Photo 选择器取消回调
    onLivePhotoSelectorCancel() {
      this.showLivePhotoSelector = false
      if (this.currentLivePhotoSelectorResolve) {
        this.currentLivePhotoSelectorResolve(null)
        this.currentLivePhotoSelectorResolve = null
      }
    },

    // 取消当前下载任务
    cancelCurrentDownload() {
      if (this.currentDownloadTask) {
        try {
          this.currentDownloadTask.abort()
        } catch (error) {
          console.error('取消下载任务失败:', error)
        }
        this.currentDownloadTask = null
        this.hideCustomLoading()
      }
    },

    // 返回上一页
    goBack() {
      // 返回前先取消下载
      this.cancelCurrentDownload()
      uni.navigateBack()
    },


    
    // 获取视频源
    getVideoSrc(processedData) {
      if (processedData && processedData.data) {
        if (processedData.isUrl) {
          return processedData.data
        }
        if (processedData.data.startsWith('data:')) {
          return processedData.data
        }
        return `data:${processedData.type};base64,${processedData.data}`
      }
      return ''
    },
    
    // 获取优化的文件大小显示
    getOptimizedDataSize(processedData) {
      if (!processedData || !processedData.data) {
        return '未知大小'
      }

      if (processedData.isUrl) {
        // 对于URL格式，尝试从已缓存的大小信息获取
        if (this.cachedFileSize && this.cachedFileSize > 0) {
          return this.formatFileSize(this.cachedFileSize)
        }

        // 🔧 优化：根据视频时长智能估算大小
        if (processedData.duration && processedData.duration > 0) {
          // 将duration从毫秒转换为秒
          const durationInSeconds = processedData.duration > 1000 ?
            Math.round(processedData.duration / 1000) : processedData.duration

          // 根据平台调整估算参数（单位：字节/秒）
          let bitrate = 100 * 1024 // 默认每秒100KB (800kbps)

          if (processedData.data.includes('douyin') || processedData.data.includes('aweme')) {
            bitrate = 150 * 1024 // 抖音通常码率更高 (1.2Mbps)
          } else if (processedData.data.includes('kuaishou')) {
            bitrate = 120 * 1024 // 快手中等码率 (960kbps)
          } else if (processedData.data.includes('xiaohongshu')) {
            bitrate = 130 * 1024 // 小红书码率较高 (1Mbps)
          } else if (processedData.data.includes('weishi') || processedData.data.includes('qq.com')) {
            bitrate = 110 * 1024 // 微视码率适中 (880kbps)
          }

          const estimatedSize = durationInSeconds * bitrate
          return `约 ${this.formatFileSize(estimatedSize)}`
        }

        // 🔧 如果正在加载，显示"获取中..."；如果加载失败，显示"未知大小"
        if (this.loadingSizePromise) {
          return '获取中...'
        } else {
          return '未知大小'
        }
      }

      // Base64格式，计算实际大小
      const base64Data = processedData.data.replace(/^data:[^;]+;base64,/, '')
      const sizeInBytes = (base64Data.length * 3) / 4
      return this.formatFileSize(sizeInBytes)
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes < 1024) {
        return `${Math.round(bytes)} B`
      } else if (bytes < 1024 * 1024) {
        return `${Math.round(bytes / 1024)} KB`
      } else if (bytes < 1024 * 1024 * 1024) {
        return `${Math.round(bytes / (1024 * 1024) * 10) / 10} MB`
      } else {
        return `${Math.round(bytes / (1024 * 1024 * 1024) * 10) / 10} GB`
      }
    },

    // 估算视频大小
    estimateVideoSize(durationSeconds) {
      // 根据视频时长估算大小
      // 使用固定码率估算
      const bitrate = 2500000 // 统一使用 2.5Mbps 估算
      return Math.round((durationSeconds * bitrate) / 8) // 转换为字节
    },

    // 获取优化的处理说明
    getOptimizedNote(resultData) {
      if (!resultData) return '未知'

      const type = resultData.type
      const processedData = resultData.processedData

      if (type === 'text') {
        // 纯文本内容
        const text = this.resultData.content || this.resultData.description || ''
        const textLength = text.length
        return `纯文本内容 (${textLength}字符)`
      } else if (type === 'image') {
        // 图文内容
        const imageCount = processedData.imageUrls ? processedData.imageUrls.length : 1
        return `高清图集 (${imageCount}张)`
      } else if (type === 'video') {
        // 视频内容
        let description = ''

        // 判断是否无水印
        if (resultData.note && resultData.note.includes('无水印')) {
          description = '无水印视频'
        } else {
          description = '视频内容'
        }

        // 添加时长信息
        if (processedData.duration && processedData.duration > 0) {
          // 将duration从毫秒转换为秒
          const durationInSeconds = processedData.duration > 1000 ?
            Math.round(processedData.duration / 1000) : processedData.duration

          const minutes = Math.floor(durationInSeconds / 60)
          const seconds = durationInSeconds % 60
          if (minutes > 0) {
            description += ` (${minutes}分${seconds}秒)`
          } else {
            description += ` (${seconds}秒)`
          }
        }

        return description
      }

      return resultData.note || '处理完成'
    },
    
    // 视频事件处理
    onVideoLoadStart() {
      this.videoStatus = '正在加载视频...'
    },
    
    onVideoCanPlay() {
      this.videoStatus = '视频加载完成'
      setTimeout(() => {
        this.videoStatus = ''
      }, 2000)
    },
    
      onVideoError(e) {
      console.error('视频加载失败:', e)
        
      this.videoStatus = '视频加载失败，建议保存到相册后播放'

      if (this.isPageActive) {
        uni.showModal({
          title: '播放失败',
          content: '视频文件较大无法在线播放，建议保存到相册后观看或复制链接到浏览器下载',
          showCancel: true,
          cancelText: '复制链接',
          confirmText: '保存到相册',
          success: (res) => {
            if (res.confirm) {
              this.saveToAlbum()
            } else {
              // 复制视频链接作为备用方案
              this.copyVideoUrl()
            }
          }
        })
      }
    },

    // 主图加载事件
    onMainImageLoad() {
      this.imageLoading = false

      // 将当前图片添加到预加载集合
      const currentImageUrl = this.getCurrentImageUrl()
      if (currentImageUrl) {
        this.preloadedImages.add(currentImageUrl)
      }
    },

    onMainImageError(e) {
      // 静默处理主图加载失败
      this.imageLoading = false
      if (this.isPageActive) {
        uni.showToast({
          title: '图片加载失败',
          icon: 'none'
        })
      }
    },

    // 预览当前图片（点击大图时调用）
    previewCurrentImage() {
      const imageUrls = this.resultData.processedData?.imageUrls || []
      if (imageUrls.length === 0) {
        return
      }

      // 使用当前图片索引而不是URL，这样更稳定

      uni.previewImage({
        current: this.currentImageIndex, // 使用索引
        urls: imageUrls,
        success: () => {
        },
        fail: (error) => {
          console.error('预览失败:', error)
          // 如果索引方式失败，尝试URL方式
          const currentImageUrl = this.getCurrentImageUrl()
          uni.previewImage({
            current: currentImageUrl,
            urls: imageUrls
          })
        }
      })
    },

    // 保存到相册
    async saveToAlbum() {
      if (!this.resultData.processedData) {
        if (this.isPageActive) {
          uni.showToast({
            title: '没有可保存的内容',
            icon: 'none'
          })
        }
        return
      }

      // 🔧 关键修复：在显示广告前就检查相册权限（确保在用户交互上下文中）
      try {
        await this.checkAlbumPermission()
        console.log('[权限] 相册权限检查通过')
      } catch (error) {
        console.error('[权限] 相册权限检查失败:', error)
        if (this.isPageActive) {
          uni.showModal({
            title: '需要相册权限',
            content: '保存视频需要访问您的相册，请在设置中开启相册权限',
            showCancel: false
          })
        }
        return
      }

      // 🎯 优化广告逻辑：判断是否需要用户后续选择
      const needsUserSelection = this.checkIfNeedsUserSelection()

      if (!needsUserSelection) {
        // 直接下载的情况：纯视频或单个静态图片，立即显示广告
        console.log('[广告逻辑] 直接下载场景，立即显示广告')
        const adResult = await adManager.showInterstitialAd('saveToAlbum')
        if (!adResult) {
          return // 用户取消了操作
        }

        // 🔒 广告完成后，检查是否需要强制分享
        const sharePermission = shareManager.checkActionPermission('saveToAlbum')
        if (!sharePermission.allowed) {
          console.log('[强制分享] 保存操作需要分享权限')
          return new Promise((resolve) => {
            shareManager.showForceShareDialog('saveToAlbum', (result) => {
              if (result.allowed) {
                console.log('[强制分享] 分享成功，继续保存操作')
                // 递归调用自己，此时应该有权限了
                this.saveToAlbum().then(resolve)
              } else {
                console.log('[强制分享] 用户取消分享，停止保存操作')
                resolve()
              }
            })
          })
        }
      } else {
        // 需要用户选择的情况：延迟显示广告，等用户选择后再显示
        console.log('[广告逻辑] 需要用户选择场景，延迟显示广告')
      }

      try {
        // 优先根据内容类型判断保存逻辑
        if (this.resultData.type === 'image') {
          // 🎯 图文内容：根据是否需要选择来决定是否跳过广告
          const skipAd = needsUserSelection // 如果需要选择，跳过广告（等选择后再显示）
          await this.saveImageContent(skipAd)
          return
        }

        // 视频内容或其他类型，继续执行下面的逻辑
        console.log(`[保存逻辑] 处理类型: ${this.resultData.type}, processedData.type: ${this.resultData.processedData?.type}`)

        this.showCustomLoading('开始下载，请耐心等待...')

        if (this.resultData.processedData.isUrl) {
          // URL格式，先检查文件大小


          // 检查文件大小
          const fileSize = await this.checkVideoSize(this.resultData.processedData.data)
          const duration = this.resultData.processedData.duration || 0

          console.log(`[大文件检测] 文件大小: ${Math.round(fileSize / 1024 / 1024)}MB, 时长: ${duration}秒`)

          if (fileSize > 30 * 1024 * 1024) {
            console.log('[大文件检测] 触发大文件提醒')
            
            // 检查页面是否活跃
            if (!this.isPageActive) {
              console.log('[大文件检测] 页面已不活跃，跳过提醒')
              // 页面不活跃时，直接继续下载，不显示提醒
            } else {
              const fileSizeMB = fileSize / 1024 / 1024
              const confirmResult = await this.showLargeFileDialog(fileSizeMB)

              if (!confirmResult) {
                this.hideCustomLoading()
                return
              }
            }
          } else {
            console.log('[大文件检测] 文件大小正常，无需提醒')
          }

          let downloadResult
          try {
            downloadResult = await this.downloadVideo(this.resultData.processedData.data)
          } catch (error) {
            // 检查是否需要重新解析
            if (error.message.startsWith('NEED_REPARSE:')) {
              try {
                // 静默重新解析，不显示任何提示
                await this.reParseVideo()
                // 重新解析成功后，递归调用保存方法
                this.saveToAlbum()
                return
              } catch (reparseError) {
                console.error('自动重新解析失败:', reparseError)

                // 如果重新解析失败，尝试直接使用当前URL下载（可能是临时网络问题）
                try {
                  // 移除NEED_REPARSE前缀，直接尝试下载
                  const originalUrl = this.resultData.processedData.data
                  const directDownloadResult = await this.downloadVideo(originalUrl, 0, true)

                  // 如果直接下载成功，继续保存流程
                  downloadResult = directDownloadResult
                } catch (directDownloadError) {
                  console.error('直接下载也失败:', directDownloadError)
                  // 如果直接下载也失败，抛出原始错误
                  throw error
                }
              }
            } else {
              throw error
            }
          }

          // 保存到相册
          await new Promise((resolve, reject) => {
            uni.saveVideoToPhotosAlbum({
              filePath: downloadResult,
              success: (res) => {
                resolve(res)
              },
              fail: (error) => {
                console.error('保存到相册失败:', error)

                // 检查是否是开发工具的问题
                if (error.errMsg && (error.errMsg.includes('fail') || error.errMsg.includes('ENOENT') || error.errMsg.includes('.json'))) {
                  reject(new Error('🛠️ 开发工具下载限制：这是微信开发工具的已知问题，真机测试正常。建议：1️⃣ 真机测试 2️⃣ 复制链接到浏览器下载'))
                } else {
                  reject(new Error('保存到相册失败，请检查相册权限'))
                }
              }
            })
          })

        } else {
          // Base64格式，提示手动保存
          this.hideCustomLoading()
          if (this.isPageActive) {
            uni.showModal({
              title: '保存提示',
              content: '请长按视频选择"保存视频"来手动保存到相册',
              showCancel: false
            })
          }
          return
        }

        this.hideCustomLoading()

        // 设置保存路径
        this.setSavedPath('video')

        // 只有在页面还活跃时才显示成功提示
        if (this.isPageActive) {
          uni.showModal({
            title: '保存成功',
            content: '视频已成功保存到您的相册中',
            showCancel: false,
            confirmText: '知道了'
          })
        }

      } catch (error) {
        console.error('保存过程出错:', error)
        this.hideCustomLoading()

        // 清理下载任务引用
        this.currentDownloadTask = null

        // 如果页面已离开或错误信息包含"页面已离开"，不显示错误提示
        if (!this.isPageActive || (error.message && error.message.includes('页面已离开'))) {
          console.log('页面已离开，不显示错误提示')
          return
        }

        let errorMessage = '保存失败'
        let suggestions = []

        if (error.message.includes('权限')) {
          errorMessage = '相册权限被拒绝'
          suggestions.push('请在手机设置中开启相册权限')
        } else if (error.message.includes('失效') || error.message.includes('无法访问') || error.message.includes('不是视频文件')) {
          errorMessage = '视频链接已失效'
          suggestions.push('尝试重新解析获取新链接')
          suggestions.push('或复制链接到浏览器下载')
        } else if (error.message.includes('超时') || error.message.includes('网络')) {
          errorMessage = '网络连接超时'
          suggestions.push('请检查网络连接')
          suggestions.push('建议在WiFi环境下重试')
        } else if (error.message.includes('下载')) {
          errorMessage = '视频下载失败'
          suggestions.push('请检查网络连接')
          suggestions.push('可能是视频文件较大')
        } else if (error.message.includes('文件')) {
          errorMessage = '文件处理失败'
          suggestions.push('可能是存储空间不足')
        }

        const content = suggestions.length > 0
          ? `${errorMessage}\n\n建议：\n${suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n\n也可复制链接到浏览器下载`
          : `${errorMessage}，建议检查网络连接或在WiFi环境下重试，也可复制链接到浏览器下载`

        // 简化错误处理，不再提供手动重新解析选项
        uni.showModal({
          title: '保存失败',
          content: content,
          showCancel: true,
          cancelText: '复制链接',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.saveToAlbum() // 重试
            } else {
              // 复制视频链接作为备用方案
              this.copyVideoUrl()
            }
          }
        })
      }
    },

    // 🎯 检查是否需要用户选择（用于优化广告显示时机）
    checkIfNeedsUserSelection() {
      console.log('[广告逻辑] 开始检查是否需要用户选择')
      console.log('[广告逻辑] 内容类型:', this.resultData.type)

      // 纯视频内容：直接下载，不需要选择
      if (this.resultData.type?.includes('video')) {
        console.log('[广告逻辑] 纯视频内容，不需要用户选择')
        return false
      }

      // 图文内容：需要进一步判断
      if (this.resultData.type === 'image') {
        const imageUrls = this.resultData.processedData.imageUrls || [this.resultData.processedData.data]
        const videoUrls = this.resultData.processedData.videoUrls || this.resultData.processedData.livePhotoVideos || []
        const totalImages = imageUrls.length
        const totalVideos = videoUrls.length

        console.log('[广告逻辑] 图文内容分析:', { totalImages, totalVideos })

        // 有Live Photo视频：需要用户选择保存什么
        if (totalVideos > 0) {
          console.log('[广告逻辑] 有Live Photo视频，需要用户选择')
          return true
        }

        // 多张图片：需要用户选择保存哪些
        if (totalImages > 1) {
          console.log('[广告逻辑] 多张图片，需要用户选择')
          return true
        }

        // 单张静态图片：直接下载，不需要选择
        console.log('[广告逻辑] 单张静态图片，不需要用户选择')
        return false
      }

      // 其他情况：保守起见，认为需要选择
      console.log('[广告逻辑] 其他情况，保守认为需要用户选择')
      return true
    },

    // 保存图文内容
    async saveImageContent(skipAd = false) {
      try {
        console.log('[广告逻辑] saveImageContent 被调用，skipAd:', skipAd)

        // 🎯 如果还没有显示过广告，现在显示
        if (!skipAd) {
          console.log('[广告逻辑] 需要显示广告，现在显示')
          const adResult = await adManager.showInterstitialAd('saveToAlbum')
          if (!adResult) {
            console.log('[广告逻辑] 用户取消了广告，停止操作')
            return // 用户取消了操作
          }
          console.log('[广告逻辑] 用户观看了广告，继续操作')
        } else {
          console.log('[广告逻辑] 跳过广告显示（已经显示过或不需要显示）')
        }

        const imageUrls = this.resultData.processedData.imageUrls || [this.resultData.processedData.data]
        // 同时检查videoUrls和livePhotoVideos字段，确保兼容性
        const videoUrls = this.resultData.processedData.videoUrls || this.resultData.processedData.livePhotoVideos || []
        const totalImages = imageUrls.length
        const totalVideos = videoUrls.length



        if (totalImages === 0 && totalVideos === 0) {
          uni.showToast({
            title: '没有可保存的内容',
            icon: 'none'
          })
          return
        }

        // 如果有Live Photo视频，询问用户要保存什么
        if (totalVideos > 0) {
          const choice = await this.showSaveChoiceDialog(totalImages, totalVideos)
          if (choice === 'cancel') {
            return
          } else if (choice === 'videos') {
            // 选择要保存的Live Photo视频
            await this.selectAndSaveLivePhotoVideos(videoUrls)
            return
          } else if (choice === 'both') {
            // 保存图片和视频
            await this.saveImagesAndVideos(imageUrls, videoUrls)
            return
          }
          // choice === 'images' 时继续执行下面的图片保存逻辑
        }

      // 多张图片时显示选择界面
      if (totalImages > 1) {
        const selectedIndexes = await this.showImageSelectorDialog(imageUrls)

        if (!selectedIndexes || selectedIndexes.length === 0) {
          return
        }

        // 保存选中的图片
        await this.saveSelectedImages(imageUrls, selectedIndexes)
        return
      }

      // 保存所有图片（单张图片的情况，已经显示过广告了）
      // 显示批量保存的加载遮罩
      this.showCustomLoading(`正在导出图片，请稍候...`)

      let successCount = 0
      let failCount = 0

      for (let i = 0; i < imageUrls.length; i++) {
        try {
          // 更新进度提示
          this.showCustomLoading(`正在导出第 ${i + 1}/${totalImages} 张图片`)
          await this.saveImageToAlbumSilent(imageUrls[i], i + 1)
          successCount++

          // 添加延迟，避免保存过快
          if (i < imageUrls.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        } catch (error) {
          console.error(`保存第${i + 1}张图片失败:`, error)
          failCount++
        }
      }

      console.log(`批量保存完成，成功: ${successCount}张，失败: ${failCount}张`)

      this.hideCustomLoading()

        // 显示保存结果
        if (successCount === totalImages) {
          // 设置保存路径
          this.setSavedPath('image')

          uni.showModal({
            title: '保存成功',
            content: `已成功保存${successCount}张图片到您的相册中`,
            showCancel: false,
            confirmText: '知道了'
          })
        } else if (successCount > 0) {
          // 部分成功也设置保存路径
          this.setSavedPath('image')

          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}张图片，${failCount}张失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有图片保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存图文内容过程中出错:', error)
        this.hideCustomLoading()

        // 忽略微信配置相关的错误
        if (error.message && error.message.includes('mp-weixin.oauth.weixin.appid')) {
  
          return
        }

        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示保存选择对话框
    async showSaveChoiceDialog(imageCount, videoCount) {
      return new Promise((resolve) => {
        const actions = []

        if (imageCount > 0) {
          actions.push(`保存图片 (${imageCount}张)`)
        }
        if (videoCount > 0) {
          actions.push(`保存Live Photo视频 (${videoCount}个)`)
        }
        if (imageCount > 0 && videoCount > 0) {
          actions.push(`保存全部 (${imageCount}张图片 + ${videoCount}个视频)`)
        }

        uni.showActionSheet({
          itemList: actions,
          success: (res) => {
            const index = res.tapIndex
            if (imageCount > 0 && videoCount > 0) {
              // 有图片和视频
              if (index === 0) resolve('images')
              else if (index === 1) resolve('videos')
              else if (index === 2) resolve('both')
            } else if (imageCount > 0) {
              // 只有图片
              resolve('images')
            } else {
              // 只有视频
              resolve('videos')
            }
          },
          fail: () => {
            resolve('cancel')
          }
        })
      })
    },

    // 选择并保存Live Photo视频
    async selectAndSaveLivePhotoVideos(videoUrls) {
      try {

        // 显示Live Photo选择界面
        const selectedIndexes = await this.showLivePhotoSelectorDialog(videoUrls)

        if (!selectedIndexes || selectedIndexes.length === 0) {
          return
        }

        // 获取选中的视频URL - 从livePhotoVideos数组中获取，而不是videoUrls
        const imageUrls = this.resultData.processedData?.imageUrls || []
        const livePhotoVideos = this.resultData.processedData?.livePhotoVideos || []
        
        // 根据选中的索引获取对应的视频URL
        const selectedVideoUrls = []
        const selectedVideoInfo = []
        
        for (let i = 0; i < selectedIndexes.length; i++) {
          const imageIndex = selectedIndexes[i]  // 这是图片在原数组中的索引
          const videoUrl = livePhotoVideos[imageIndex]  // 从livePhotoVideos数组中获取对应视频URL
          
          if (videoUrl && typeof videoUrl === 'string' && videoUrl.trim() !== '') {
            selectedVideoUrls.push(videoUrl)
            selectedVideoInfo.push({ url: videoUrl, index: imageIndex })
          } else {
            console.error(`第${imageIndex + 1}个Live Photo视频URL无效:`, videoUrl)
          }
        }



        // 保存选中的视频（不跳过广告，在这里显示广告）
        await this.saveLivePhotoVideos(selectedVideoUrls, selectedVideoInfo, false)

      } catch (error) {
        console.error('选择和保存Live Photo视频失败:', error)
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示Live Photo选择对话框
    async showLivePhotoSelectorDialog(videoUrls) {
      return new Promise((resolve) => {

        // 传递正确的数据给选择器
        this.livePhotoSelectorVideos = videoUrls
        this.livePhotoSelectorImages = this.resultData.processedData?.imageUrls || []
        this.livePhotoSelectorLiveVideos = this.resultData.processedData?.livePhotoVideos || videoUrls || []
        this.showLivePhotoSelector = true
        this.currentLivePhotoSelectorResolve = resolve
      })
    },

    // 保存Live Photo视频
    async saveLivePhotoVideos(videoUrls, videoInfo = null, skipAd = false) {
      try {
        // 🎯 如果还没有显示过广告，现在显示
        if (!skipAd) {
          console.log('[广告逻辑] 用户选择Live Photo完成，现在显示广告')
          const adResult = await adManager.showInterstitialAd('saveToAlbum')
          if (!adResult) {
            return // 用户取消了操作
          }
        }

        // 🔒 广告完成后，检查是否需要强制分享
        const sharePermission = shareManager.checkActionPermission('saveToAlbum')
        if (!sharePermission.allowed) {
          console.log('[强制分享] 保存操作需要分享权限')
          return new Promise((resolve) => {
            shareManager.showForceShareDialog('saveToAlbum', (result) => {
              if (result.allowed) {
                console.log('[强制分享] 分享成功，继续保存操作')
                // 递归调用自己，此时应该有权限了
                this.saveLivePhotoVideos(videoUrls, videoInfo, true).then(resolve) // skipAd=true 避免重复显示广告
              } else {
                console.log('[强制分享] 用户取消分享，停止保存操作')
                resolve()
              }
            })
          })
        }

        let successCount = 0
        let failCount = 0

        // 显示批量下载的加载遮罩
        this.showCustomLoading(`正在导出 Live Photo 视频，请稍候...`)

        for (let i = 0; i < videoUrls.length; i++) {
          try {
            const videoUrl = videoUrls[i]
            const displayIndex = videoInfo ? videoInfo[i].index + 1 : i + 1

            console.log(`开始保存第${i + 1}个Live Photo视频 (Live Photo ${displayIndex}):`, videoUrl)
            
            // 检查videoUrl是否有效
            if (!videoUrl || typeof videoUrl !== 'string' || videoUrl.trim() === '') {
              console.error(`第${i + 1}个Live Photo视频URL无效:`, videoUrl)
              failCount++
              continue
            }

            // 更新进度提示
            this.showCustomLoading(`正在导出第 ${i + 1}/${videoUrls.length} 个 Live Photo 视频`)

            // 使用修复后的downloadLivePhoto方法，但不显示单独的加载提示
            await this.downloadLivePhotoSilent(videoUrl, videoInfo ? videoInfo[i].index : i)
            successCount++

            // 添加延迟，避免并发过多
            if (i < videoUrls.length - 1) {
              await new Promise(resolve => {
                setTimeout(() => {
                  resolve()
                }, 1000)
              })
            }
          } catch (error) {
            console.error(`保存第${i + 1}个Live Photo视频失败:`, error)
            failCount++
          }
        }

        // 隐藏加载遮罩
        this.hideCustomLoading()

        // 显示保存结果
        if (successCount === videoUrls.length) {
          // 设置保存路径
          this.setSavedPath('video')

          uni.showModal({
            title: '保存成功',
            content: `已成功保存${successCount}个Live Photo视频到您的相册中`,
            showCancel: false
          })
        } else if (successCount > 0) {
          // 部分成功也设置保存路径
          this.setSavedPath('video')

          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}个Live Photo视频，${failCount}个失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有Live Photo视频保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存Live Photo视频过程中出错:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 保存图片和视频
    async saveImagesAndVideos(imageUrls, videoUrls, skipAd = false) {
      try {
        // 🎯 如果还没有显示过广告，现在显示
        if (!skipAd) {
          console.log('[广告逻辑] 用户选择保存全部，现在显示广告')
          const adResult = await adManager.showInterstitialAd('saveToAlbum')
          if (!adResult) {
            return // 用户取消了操作
          }
        }

        // 🔒 广告完成后，检查是否需要强制分享
        const sharePermission = shareManager.checkActionPermission('saveToAlbum')
        if (!sharePermission.allowed) {
          console.log('[强制分享] 保存操作需要分享权限')
          return new Promise((resolve) => {
            shareManager.showForceShareDialog('saveToAlbum', (result) => {
              if (result.allowed) {
                console.log('[强制分享] 分享成功，继续保存操作')
                // 递归调用自己，此时应该有权限了
                this.saveImagesAndVideos(imageUrls, videoUrls, true).then(resolve) // skipAd=true 避免重复显示广告
              } else {
                console.log('[强制分享] 用户取消分享，停止保存操作')
                resolve()
              }
            })
          })
        }

        const totalItems = imageUrls.length + videoUrls.length
        let currentIndex = 0
        let successCount = 0
        let failCount = 0

        // 先保存图片
        for (let i = 0; i < imageUrls.length; i++) {
          try {
            currentIndex++
            this.showCustomLoading(`正在保存内容 ${currentIndex}/${totalItems} (图片 ${i + 1})`)
            await this.saveImageToAlbum(imageUrls[i], i + 1, imageUrls.length)
            successCount++

            if (currentIndex < totalItems) {
              await new Promise(resolve => setTimeout(resolve, 500))
            }
          } catch (error) {
            console.error(`保存第${i + 1}张图片失败:`, error)
            failCount++
          }
        }

        // 再保存视频
        for (let i = 0; i < videoUrls.length; i++) {
          try {
            currentIndex++
            this.showCustomLoading(`正在保存内容 ${currentIndex}/${totalItems} (Live Photo ${i + 1})`)

            // 使用云函数下载Live Photo
            await this.downloadLivePhotoSilent(videoUrls[i], i)
            successCount++

            if (currentIndex < totalItems) {
              console.log(`等待1秒后下载下一个Live Photo...`)
              await new Promise(resolve => {
                setTimeout(() => {
                  console.log(`延迟结束，准备下载下一个内容`)
                  resolve()
                }, 1000)
              })
            }
          } catch (error) {
            console.error(`保存第${i + 1}个Live Photo视频失败:`, error)
            failCount++
          }
        }

        this.hideCustomLoading()

        // 显示保存结果
        if (successCount === totalItems) {
          // 设置保存路径
          this.setSavedPath('mixed')

          uni.showModal({
            title: '保存成功',
            content: `已成功保存${imageUrls.length}张图片和${videoUrls.length}个Live Photo视频到您的相册中`,
            showCancel: false
          })
        } else if (successCount > 0) {
          // 部分成功也设置保存路径
          this.setSavedPath('mixed')

          uni.showModal({
            title: '部分保存成功',
            content: `成功保存${successCount}项内容，${failCount}项失败`,
            showCancel: false
          })
        } else {
          uni.showModal({
            title: '保存失败',
            content: '所有内容保存失败，请检查网络连接和相册权限',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('保存图片和视频过程中出错:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '保存失败',
          content: `保存过程出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 显示图片选择器对话框
    async showImageSelectorDialog(imageUrls) {
      return new Promise((resolve) => {
        // 如果只有一张图片，直接保存
        if (imageUrls.length === 1) {
          resolve([0])
          return
        }

        // 直接显示可视化选择器
        this.selectorImages = imageUrls
        this.showImageSelector = true
        this.currentImageSelectorResolve = resolve
      })
    },

    // 保存选中的图片
    async saveSelectedImages(imageUrls, selectedIndexes) {
      // 🎯 用户选择完成，现在显示广告
      console.log('[广告逻辑] 用户选择图片完成，现在显示广告')
      const adResult = await adManager.showInterstitialAd('saveToAlbum')
      if (!adResult) {
        return // 用户取消了操作
      }

      // 🔒 广告完成后，检查是否需要强制分享
      const sharePermission = shareManager.checkActionPermission('saveToAlbum')
      if (!sharePermission.allowed) {
        console.log('[强制分享] 保存操作需要分享权限')
        return new Promise((resolve) => {
          shareManager.showForceShareDialog('saveToAlbum', (result) => {
            if (result.allowed) {
              console.log('[强制分享] 分享成功，继续保存操作')
              // 递归调用自己，此时应该有权限了
              this.saveSelectedImages(imageUrls, selectedIndexes).then(resolve)
            } else {
              console.log('[强制分享] 用户取消分享，停止保存操作')
              resolve()
            }
          })
        })
      }

      const totalSelected = selectedIndexes.length
      console.log(`开始保存选中的${totalSelected}张图片`)

      // 显示批量保存的加载遮罩
      this.showCustomLoading(`正在导出图片，请稍候...`)

      let successCount = 0
      let failCount = 0

      for (let i = 0; i < selectedIndexes.length; i++) {
        const imageIndex = selectedIndexes[i]
        const imageUrl = imageUrls[imageIndex]

        try {
          console.log(`开始保存第${imageIndex + 1}张图片:`, imageUrl)
          // 更新进度提示
          this.showCustomLoading(`正在导出第 ${i + 1}/${totalSelected} 张图片`)
          await this.saveImageToAlbumSilent(imageUrl, imageIndex + 1)
          successCount++
          console.log(`第${imageIndex + 1}张图片保存成功`)

          // 添加延迟，避免保存过快
          if (i < selectedIndexes.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        } catch (error) {
          console.error(`保存第${imageIndex + 1}张图片失败:`, error)
          failCount++
        }
      }

      console.log(`批量保存完成，成功: ${successCount}张，失败: ${failCount}张`)

      this.hideCustomLoading()

      // 显示保存结果
      if (successCount === totalSelected) {
        // 设置保存路径
        this.setSavedPath('image')

        uni.showModal({
          title: '保存成功',
          content: `已成功保存${successCount}张图片到您的相册中`,
          showCancel: false,
          confirmText: '知道了'
        })
      } else if (successCount > 0) {
        // 部分成功也设置保存路径
        this.setSavedPath('image')

        uni.showModal({
          title: '部分保存成功',
          content: `成功保存${successCount}张图片，${failCount}张失败`,
          showCancel: false
        })
      } else {
        uni.showModal({
          title: '保存失败',
          content: '所有图片保存失败，请检查网络连接和相册权限',
          showCancel: false
        })
      }
    },

    // 静默保存单张图片到相册（用于批量保存，不显示单独的提示）
    async saveImageToAlbumSilent(imageUrl, current) {
      return new Promise(async (resolve, reject) => {
        try {
          console.log(`开始下载第${current}张图片:`, imageUrl)

          // 使用file-downloader云函数下载图片
          const downloadResult = await uniCloud.callFunction({
            name: 'file-downloader',
            data: {
              action: 'downloadFile',
              url: imageUrl,
              options: {
                maxSize: 50 * 1024 * 1024, // 50MB限制
                timeout: 30000
              }
            }
          })

          if (!downloadResult.result || !downloadResult.result.success) {
            throw new Error(downloadResult.result?.error || '下载失败')
          }

          const { data: base64Data, contentType } = downloadResult.result

          // 将base64数据写入临时文件
          const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_image_${Date.now()}_${current}.jpg`
          const fs = wx.getFileSystemManager()

          // 移除base64前缀
          const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

          fs.writeFileSync(tempFilePath, pureBase64, 'base64')

          // 保存到相册
          uni.saveImageToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              console.log(`第${current}张图片保存成功`)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              resolve()
            },
            fail: (error) => {
              console.error(`第${current}张图片保存到相册失败:`, error)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              reject(new Error(`第${current}张图片保存失败`))
            }
          })
        } catch (error) {
          console.error(`第${current}张图片下载失败:`, error)
          reject(new Error(`第${current}张图片下载失败: ${error.message}`))
        }
      })
    },

    // 保存单张图片到相册
    async saveImageToAlbum(imageUrl, current) {
      return new Promise(async (resolve, reject) => {
        try {
          console.log(`开始下载第${current}张图片:`, imageUrl)

          // 使用file-downloader云函数下载图片
          const downloadResult = await uniCloud.callFunction({
            name: 'file-downloader',
            data: {
              action: 'downloadFile',
              url: imageUrl,
              options: {
                maxSize: 50 * 1024 * 1024, // 50MB限制
                timeout: 30000
              }
            }
          })

          if (!downloadResult.result || !downloadResult.result.success) {
            throw new Error(downloadResult.result?.error || '下载失败')
          }

          const { data: base64Data } = downloadResult.result

          // 将base64数据写入临时文件
          const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_image_${Date.now()}_${current}.jpg`
          const fs = wx.getFileSystemManager()

          // 移除base64前缀
          const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

          fs.writeFileSync(tempFilePath, pureBase64, 'base64')

          // 保存到相册
          uni.saveImageToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              console.log(`第${current}张图片保存成功`)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              resolve()
            },
            fail: (error) => {
              console.error(`第${current}张图片保存到相册失败:`, error)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              reject(new Error(`第${current}张图片保存失败`))
            }
          })
        } catch (error) {
          console.error(`第${current}张图片下载失败:`, error)
          reject(new Error(`第${current}张图片下载失败: ${error.message}`))
        }
      })
    },

    // 重新解析视频（无UI版本，用于自动重新解析）
    async reParseVideo(retryCount = 0) {
      const maxRetries = 2 // 最多重试2次

      if (!this.resultData.originalUrl) {
        throw new Error('无法重新解析：缺少原始链接')
      }

      // 检查速率限制（重新解析也要限制频率）
      const rateLimitCheck = adManager.checkRateLimit(this.resultData.originalUrl)
      if (!rateLimitCheck.allowed) {
        throw new Error(rateLimitCheck.message)
      }

      try {
        // 记录请求
        adManager.recordRequest(this.resultData.originalUrl)
        
        // 使用统一解析器重新解析
        const result = await uniCloud.callFunction({
          name: 'unified-parser',
          data: {
            link: this.resultData.originalUrl,
            options: {
              debug: false
            }
          }
        })

        if (result.result && result.result.title) {
          // 统一解析器直接返回标准化结果
          this.resultData = result.result
          console.log('重新解析成功，已更新结果数据')
          return this.resultData
        } else {
          throw new Error(result.result?.content || result.result?.message || '重新解析失败')
        }
      } catch (error) {
        console.error(`重新解析失败 (第${retryCount + 1}次):`, error)

        // 如果还有重试次数，等待一下再重试
        if (retryCount < maxRetries) {
          // 随机延迟1-3秒，避免被识别为机器人
          const delay = 1000 + Math.random() * 2000
          console.log(`等待${Math.round(delay/1000)}秒后进行第${retryCount + 2}次重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
          return await this.reParseVideo(retryCount + 1)
        } else {
          throw error
        }
      }
    },

    // 图片预览相关方法
    // 获取当前显示的图片URL
    getCurrentImageUrl() {
      if (this.resultData.processedData?.imageUrls && this.resultData.processedData.imageUrls.length > 0) {
        const url = this.resultData.processedData.imageUrls[this.currentImageIndex] || this.resultData.processedData.imageUrls[0];
        
        // 调试：打印URL信息
        console.log('🔍 前端getCurrentImageUrl调试:', {
          当前索引: this.currentImageIndex,
          URL类型: typeof url,
          URL内容: url,
          前3个URLs: this.resultData.processedData.imageUrls.slice(0, 3).map(u => ({ 类型: typeof u, 内容: u }))
        });
        
        return url;
      }
      return this.resultData.processedData?.data || ''
    },

    // 选择图片
    selectImage(index) {
      

      // 如果选择的是当前图片，不需要切换
      if (this.currentImageIndex === index) {
        return
      }

      const imageUrl = this.resultData.processedData.imageUrls[index]

      // 如果图片已经预加载，直接切换
      if (this.preloadedImages.has(imageUrl)) {
        this.currentImageIndex = index
      } else {
        // 显示加载状态
        this.imageLoading = true
        this.currentImageIndex = index
      }

      // 可以添加一些反馈效果
      uni.vibrateShort({
        type: 'light'
      })

      // 预加载相邻的图片
      this.preloadAdjacentImages(index)
    },

    // 预加载相邻图片
    preloadAdjacentImages(currentIndex) {
      const imageUrls = this.resultData.processedData?.imageUrls || []
      const preloadIndexes = []

      // 预加载前后各2张图片
      for (let i = -2; i <= 2; i++) {
        const index = currentIndex + i
        if (index >= 0 && index < imageUrls.length && index !== currentIndex) {
          preloadIndexes.push(index)
        }
      }

      preloadIndexes.forEach(index => {
        const imageUrl = imageUrls[index]
        if (!this.preloadedImages.has(imageUrl)) {
          this.preloadImage(imageUrl)
        }
      })
    },

    // 预加载单张图片（小程序环境下使用uni.getImageInfo）
    preloadImage(imageUrl) {
      uni.getImageInfo({
        src: imageUrl,
        success: () => {
          this.preloadedImages.add(imageUrl)
        },
        fail: () => {
          // 静默处理预加载失败，避免控制台噪音
        }
      })
    },

    // Live Photo 相关方法
    // 检查指定图片是否有对应的Live Photo
    hasLivePhotoForImage(imageIndex) {
      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      const videoUrls = this.resultData.processedData?.videoUrls;
      
      // 调试：打印Live Photo数据
      if (imageIndex === 0) {
        console.log('🔍 前端Live Photo数据调试:', {
          livePhotoVideos长度: livePhotoVideos ? livePhotoVideos.length : 'N/A',
          前3个livePhotoVideos: livePhotoVideos ? livePhotoVideos.slice(0, 3).map(v => ({ 类型: typeof v, 内容: v })) : 'N/A',
          videoUrls长度: videoUrls ? videoUrls.length : 'N/A',
          前3个videoUrls: videoUrls ? videoUrls.slice(0, 3).map(v => ({ 类型: typeof v, 内容: v })) : 'N/A'
        });
      }
      
      // 如果有livePhotoVideos数据且不全为null，使用新逻辑
      const hasValidLivePhotoData = livePhotoVideos && 
                                   livePhotoVideos.length > 0 && 
                                   livePhotoVideos.some(video => video !== null);
      
      if (hasValidLivePhotoData) {
        return livePhotoVideos.length > imageIndex &&
               livePhotoVideos[imageIndex] &&
               livePhotoVideos[imageIndex] !== null;
      }
      
      // 备用逻辑：使用原来的videoUrls逻辑
      return videoUrls &&
             videoUrls.length > imageIndex &&
             videoUrls[imageIndex];
    },

    // 播放指定图片的Live Photo
    playLivePhotoForImage(imageIndex) {

      if (!this.hasLivePhotoForImage(imageIndex)) {
        uni.showToast({
          title: '该图片没有Live Photo',
          icon: 'none'
        })
        return
      }

      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      const videoUrls = this.resultData.processedData?.videoUrls;
      
      let videoUrl;
      if (livePhotoVideos && livePhotoVideos[imageIndex]) {
        videoUrl = livePhotoVideos[imageIndex];
      } else if (videoUrls && videoUrls[imageIndex]) {
        videoUrl = videoUrls[imageIndex];
      }
      
      this.currentLivePhotoIndex = imageIndex
      this.currentLivePhotoUrl = videoUrl || ''
      this.showLivePhotoPlayer = true

      console.log('打开Live Photo播放器:', videoUrl)
    },

    // 关闭Live Photo播放器
    closeLivePhotoPlayer() {
      this.showLivePhotoPlayer = false
      this.currentLivePhotoUrl = ''
      this.currentLivePhotoIndex = 0

      // 停止视频播放
      try {
        const videoContext = uni.createVideoContext('livePhotoPlayerVideo', this)
        if (videoContext) {
          videoContext.pause()
        }
      } catch (error) {
        console.error('停止视频播放失败:', error)
      }
    },

    // Live Photo播放器视频结束
    onLivePhotoPlayerEnded() {
      console.log('Live Photo播放结束')
      // 可以选择自动关闭播放器或者循环播放
      // this.closeLivePhotoPlayer()
    },

    // 🔧 重新打开Live Photo播放器（用于广告取消后恢复）
    showLivePhotoPlayerWithUrl(videoUrl, imageIndex) {
      this.currentLivePhotoUrl = videoUrl
      this.currentLivePhotoIndex = imageIndex
      this.showLivePhotoPlayer = true
    },

    // 下载当前Live Photo
    async downloadCurrentLivePhoto() {
      if (!this.currentLivePhotoUrl) {
        uni.showToast({
          title: '没有可下载的视频',
          icon: 'none'
        })
        return
      }

      // 🔧 先关闭Live Photo播放器，避免层级冲突
      const livePhotoUrl = this.currentLivePhotoUrl
      const livePhotoIndex = this.currentLivePhotoIndex
      this.closeLivePhotoPlayer()

      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('saveToAlbum')
      if (!allowed) {
        // 如果用户取消操作，重新打开Live Photo播放器
        this.showLivePhotoPlayerWithUrl(livePhotoUrl, livePhotoIndex)
        return
      }

      await this.downloadLivePhoto(livePhotoUrl, livePhotoIndex)
    },

    // 静默下载 Live Photo（用于批量下载，不显示单独的加载提示）
    async downloadLivePhotoSilent(videoUrl, index) {
      console.log('静默下载 Live Photo:', videoUrl, 'index:', index)

      return new Promise(async (resolve, reject) => {
        try {
          // 参数类型检查
          if (!videoUrl || typeof videoUrl !== 'string') {
            reject(new Error('无效的视频URL参数'))
            return
          }

          // 检查相册权限
          await this.checkAlbumPermission()

          console.log('开始使用云函数下载 Live Photo:', videoUrl)

          // 使用file-downloader云函数下载视频
          const downloadResult = await uniCloud.callFunction({
            name: 'file-downloader',
            data: {
              action: 'downloadFile',
              url: videoUrl,
              options: {
                maxSize: 200 * 1024 * 1024, // 200MB限制
                timeout: 60000 // 60秒超时
              }
            }
          })

          if (!downloadResult.result || !downloadResult.result.success) {
            throw new Error(downloadResult.result?.error || '下载失败')
          }

          const { data: base64Data } = downloadResult.result

          // 将base64数据写入临时文件
          const tempFilePath = `${wx.env.USER_DATA_PATH}/livephoto_${Date.now()}_${index}.mp4`
          const fs = wx.getFileSystemManager()

          // 移除base64前缀
          const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

          fs.writeFileSync(tempFilePath, pureBase64, 'base64')

          console.log('Live Photo 下载成功，开始保存到相册:', tempFilePath)

          // 保存到相册
          uni.saveVideoToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              console.log(`Live Photo ${index + 1} 保存到相册成功`)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              resolve()
            },
            fail: (error) => {
              console.error('保存 Live Photo 到相册失败:', error)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              reject(error)
            }
          })

        } catch (error) {
          console.error('下载 Live Photo 过程中出错:', error)
          reject(error)
        }
      })
    },

    // 下载 Live Photo
    async downloadLivePhoto(videoUrl, index) {
      console.log('开始下载 Live Photo:', videoUrl, 'index:', index)

      return new Promise(async (resolve, reject) => {
        try {
          // 参数类型检查
          if (!videoUrl || typeof videoUrl !== 'string') {
            reject(new Error('无效的视频URL参数'))
            return
          }

          // 检查相册权限
          await this.checkAlbumPermission()

          this.showCustomLoading(`正在下载 Live Photo ${index + 1}...`)

          console.log('开始使用云函数下载 Live Photo:', videoUrl)

          // 使用file-downloader云函数下载视频
          const downloadResult = await uniCloud.callFunction({
            name: 'file-downloader',
            data: {
              action: 'downloadFile',
              url: videoUrl,
              options: {
                maxSize: 200 * 1024 * 1024, // 200MB限制
                timeout: 60000 // 60秒超时
              }
            }
          })

          if (!downloadResult.result || !downloadResult.result.success) {
            throw new Error(downloadResult.result?.error || '下载失败')
          }

          const { data: base64Data } = downloadResult.result

          // 将base64数据写入临时文件
          const tempFilePath = `${wx.env.USER_DATA_PATH}/livephoto_${Date.now()}_${index}.mp4`
          const fs = wx.getFileSystemManager()

          // 移除base64前缀
          const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

          fs.writeFileSync(tempFilePath, pureBase64, 'base64')

          console.log('Live Photo 下载成功，开始保存到相册:', tempFilePath)

          // 保存到相册
          uni.saveVideoToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              console.log(`Live Photo ${index + 1} 保存到相册成功`)
              this.hideCustomLoading()
              if (this.isPageActive) {
                uni.showToast({
                  title: `Live Photo ${index + 1} 已保存`,
                  icon: 'success'
                })
              }
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              resolve()
            },
            fail: (error) => {
              console.error('保存 Live Photo 到相册失败:', error)
              this.hideCustomLoading()
              if (this.isPageActive) {
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              reject(error)
            }
          })

        } catch (error) {
          console.error('下载 Live Photo 过程中出错:', error)
          this.hideCustomLoading()
          if (this.isPageActive) {
            uni.showToast({
              title: error.message || '下载失败',
              icon: 'none'
            })
          }
          reject(error)
        }
      })
    },

    // 获取文案
    async getCopyText() {
      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('getText')
      if (!allowed) return

      let shareText = ''
      
      // 通用逻辑：有文案就组合标题和文案，没有文案就只复制标题
      if (this.resultData.content && this.resultData.content.trim()) {
        // 有文案内容时，组合标题和文案
        shareText = `${this.resultData.title}\n\n${this.resultData.content}`
      } else {
        // 没有文案时，只使用标题
        shareText = this.resultData.title
      }

      uni.setClipboardData({
        data: shareText,
        success: () => {
          if (this.isPageActive) {
            const toastTitle = '已复制文案'
            
            uni.showToast({
              title: toastTitle,
              icon: 'success'
            })
          }
        }
      })
    },

    // 获取封面
    async getCover() {
      if (!this.resultData) {
        uni.showToast({
          title: '没有可获取的封面',
          icon: 'none'
        })
        return
      }

      // 🔧 关键修复：在显示广告前就检查相册权限（确保在用户交互上下文中）
      try {
        await this.checkAlbumPermission()
        console.log('[权限] 相册权限检查通过')
      } catch (error) {
        console.error('[权限] 相册权限检查失败:', error)
        uni.showModal({
          title: '需要相册权限',
          content: '保存封面需要访问您的相册权限',
          showCancel: false
        })
        return
      }

      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('getCover')
      if (!allowed) return

      try {

        this.showCustomLoading('正在获取封面...')

        let coverUrl = null

        // 根据内容类型获取封面
        if (this.resultData.type === 'image') {
          // 图文内容，优先使用已有的封面URL
          if (this.resultData.coverUrl) {
            coverUrl = this.resultData.coverUrl
          } else {
            // 如果没有封面URL，使用备用方案
            if (this.resultData.processedData.imageUrls && this.resultData.processedData.imageUrls.length > 0) {
              coverUrl = this.resultData.processedData.imageUrls[0]
            } else if (this.resultData.processedData.data) {
              coverUrl = this.resultData.processedData.data
            }
          }
        } else if (this.resultData.type === 'video') {
          // 视频内容，优先使用已有的封面URL
          if (this.resultData.coverUrl) {
            coverUrl = this.resultData.coverUrl
          } else {
            
            if (!this.resultData.originalUrl) {
              throw new Error('缺少原始链接，无法获取封面')
            }

            try {
              // 使用统一解析器获取封面
              const result = await uniCloud.callFunction({
                name: 'unified-parser',
                data: {
                  link: this.resultData.originalUrl,
                  options: {
                    debug: false
                  }
                }
              })



              if (result.result && result.result.coverUrl) {
                coverUrl = result.result.coverUrl
              } else {
                throw new Error('云函数未返回有效的封面URL')
              }
            } catch (cloudError) {
              console.error('云函数获取封面失败:', cloudError)
              throw new Error('无法获取视频封面，请重试')
            }
          }
        }

        if (!coverUrl) {
          this.hideCustomLoading()
          uni.showToast({
            title: '无法获取封面',
            icon: 'none'
          })
          return
        }


        
        // 修复B站封面URL协议问题
        if (coverUrl) {
          // 处理相对协议URL（//domain.com）
          if (coverUrl.startsWith('//')) {
            coverUrl = 'https:' + coverUrl
          }
          // 处理HTTP协议（转换为HTTPS）
          else if (coverUrl.startsWith('http://')) {
            coverUrl = coverUrl.replace('http://', 'https://')
          }
          
          // 移除B站图片处理参数（@100w_100h_1c.png等）
          if (coverUrl.includes('@') && coverUrl.includes('hdslb.com')) {
            coverUrl = coverUrl.split('@')[0]
          }
        }

        // 下载并保存封面
        await this.saveCoverToAlbum(coverUrl)

        // 设置保存路径
        this.setSavedPath('image')

        this.hideCustomLoading()
        uni.showModal({
          title: '保存成功',
          content: '视频封面已成功保存到您的相册中',
          showCancel: false,
          confirmText: '知道了'
        })

      } catch (error) {
        console.error('获取封面失败:', error)
        this.hideCustomLoading()
        uni.showModal({
          title: '获取封面失败',
          content: error.message || '获取封面时出现错误，请重试',
          showCancel: false
        })
      }
    },

    // 保存封面到相册
    async saveCoverToAlbum(coverUrl) {
      if (!coverUrl || typeof coverUrl !== 'string') {
        console.error('❌ 无效的封面URL:', coverUrl)
        throw new Error('封面URL无效')
      }

      return new Promise(async (resolve, reject) => {
        try {
          console.log('开始下载封面:', coverUrl)

          // 使用file-downloader云函数下载封面
          const downloadResult = await uniCloud.callFunction({
            name: 'file-downloader',
            data: {
              action: 'downloadFile',
              url: coverUrl,
              options: {
                maxSize: 20 * 1024 * 1024, // 20MB限制
                timeout: 30000
              }
            }
          })

          if (!downloadResult.result || !downloadResult.result.success) {
            throw new Error(downloadResult.result?.error || '下载失败')
          }

          const { data: base64Data } = downloadResult.result

          // 将base64数据写入临时文件
          const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_cover_${Date.now()}.jpg`
          const fs = wx.getFileSystemManager()

          // 移除base64前缀
          const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

          fs.writeFileSync(tempFilePath, pureBase64, 'base64')

          // 保存到相册
          uni.saveImageToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              console.log('封面保存成功')
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              resolve()
            },
            fail: (error) => {
              console.error('封面保存到相册失败:', error)
              // 清理临时文件
              try {
                fs.unlinkSync(tempFilePath)
              } catch (e) {
                console.log('清理临时文件失败:', e)
              }
              reject(new Error('封面保存失败，请检查相册权限'))
            }
          })
        } catch (error) {
          console.error('封面下载失败:', error)
          reject(new Error(`封面下载失败: ${error.message}`))
        }
      })
    },
    
    // 复制当前显示图片的链接
    async copyCurrentImageUrl() {
      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('copyLink')
      if (!allowed) return

      this.copyImageUrl(this.currentImageIndex)
    },

    // 复制指定图片链接
    copyImageUrl(imageIndex) {
      // 检查是否有可复制的链接（URL模式或图片URLs）
      const hasUrl = this.resultData.processedData && this.resultData.processedData.isUrl;
      const hasImageUrls = this.resultData.processedData && this.resultData.processedData.imageUrls && this.resultData.processedData.imageUrls.length > 0;
      
      if (!hasUrl && !hasImageUrls) {
        if (this.isPageActive) {
          uni.showToast({
            title: '当前为本地数据，无法复制链接',
            icon: 'none'
          })
        }
        return
      }

      const imageUrls = this.resultData.processedData?.imageUrls || []
      if (imageUrls.length === 0 || !imageUrls[imageIndex]) {
        if (this.isPageActive) {
          uni.showToast({
            title: '无法获取图片链接',
            icon: 'none'
          })
        }
        return
      }

      uni.setClipboardData({
        data: imageUrls[imageIndex],
        success: () => {
          if (this.isPageActive) {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        }
      })
    },

    // 复制当前Live Photo链接
    async copyCurrentLivePhotoUrl() {
      // 🔧 先关闭Live Photo播放器，避免层级冲突
      const livePhotoUrl = this.currentLivePhotoUrl
      const livePhotoIndex = this.currentLivePhotoIndex
      this.closeLivePhotoPlayer()

      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('copyLivePhotoUrl')
      if (!allowed) {
        // 如果用户取消操作，重新打开Live Photo播放器
        this.showLivePhotoPlayerWithUrl(livePhotoUrl, livePhotoIndex)
        return
      }

      await this.copyLivePhotoUrl(livePhotoIndex)
    },

    // 复制指定Live Photo链接
    async copyLivePhotoUrl(imageIndex) {
      if (!this.hasLivePhotoForImage(imageIndex)) {
        if (this.isPageActive) {
          uni.showToast({
            title: '该图片没有Live Photo',
            icon: 'none'
          })
        }
        return
      }

      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('copyLivePhotoUrl')
      if (!allowed) return

      const livePhotoVideos = this.resultData.processedData?.livePhotoVideos;
      const videoUrls = this.resultData.processedData?.videoUrls;
      
      let videoUrl;
      if (livePhotoVideos && livePhotoVideos[imageIndex]) {
        videoUrl = livePhotoVideos[imageIndex];
      } else if (videoUrls && videoUrls[imageIndex]) {
        videoUrl = videoUrls[imageIndex];
      }

      if (!videoUrl || videoUrl === 'undefined' || videoUrl === 'null') {
        if (this.isPageActive) {
          uni.showToast({
            title: '无法获取Live Photo链接',
            icon: 'none'
          })
        }
        return
      }

      uni.setClipboardData({
        data: videoUrl,
        success: () => {
          if (this.isPageActive) {
            uni.showToast({
              title: 'Live Photo链接已复制',
              icon: 'success'
            })
          }
        }
      })
    },

    // 复制视频链接
    async copyVideoUrl() {
      // 🔐 统一权限检查
      const allowed = await this.checkOperationPermission('copyLink')
      if (!allowed) return
      
      if (this.resultData.processedData && this.resultData.processedData.isUrl) {
        let urlToCopy = this.resultData.processedData.data
        let toastTitle = '链接已复制'
        
        // 如果是图文内容，复制当前显示的图片链接
        if (this.resultData.type === 'image') {
          const imageUrls = this.resultData.processedData?.imageUrls || []
          if (imageUrls.length > 0) {
            urlToCopy = imageUrls[this.currentImageIndex] || imageUrls[0]
          }
        }
        
        uni.setClipboardData({
          data: urlToCopy,
          success: () => {
            if (this.isPageActive) {
              uni.showToast({
                title: toastTitle,
                icon: 'success'
              })
            }
          }
        })
      } else {
        if (this.isPageActive) {
          uni.showToast({
            title: '当前为本地数据，无法复制链接',
            icon: 'none'
          })
        }
      }
    },

    // 检查相册权限
    async checkAlbumPermission() {
      return new Promise((resolve, reject) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting['scope.writePhotosAlbum'] === false) {
              // 用户拒绝过权限，需要引导到设置页面
              uni.showModal({
                title: '需要相册权限',
                content: '保存视频需要访问您的相册权限，请在设置中开启',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          resolve()
                        } else {
                          reject(new Error('用户未开启相册权限'))
                        }
                      },
                      fail: () => reject(new Error('打开设置失败'))
                    })
                  } else {
                    reject(new Error('用户取消授权'))
                  }
                }
              })
            } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
              // 未授权过，直接请求授权
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => resolve(),
                fail: () => reject(new Error('用户拒绝授权'))
              })
            } else {
              // 已授权
              resolve()
            }
          },
          fail: () => reject(new Error('获取权限状态失败'))
        })
      })
    },

    // 简单直接的视频下载（使用云函数）
    async downloadVideo(videoUrl, retryCount = 0, skipReparse = false) {
      const maxRetries = 2 // 最多重试2次

      // 参数类型检查
      if (!videoUrl || typeof videoUrl !== 'string') {
        throw new Error('无效的视频URL参数')
      }

      console.log('开始下载视频:', videoUrl)
      console.log('当前重试次数:', retryCount)

      if (!videoUrl.startsWith('http')) {
        throw new Error('视频URL格式不正确')
      }

      // 检查页面是否还活跃
      if (!this.isPageActive) {
        console.log('页面已离开，停止下载')
        throw new Error('页面已离开，下载已取消')
      }

      const timeout = 120000 // 统一设置2分钟超时

      console.log('设置超时时间:', timeout / 1000, '秒')

      try {
        console.log('开始使用云函数下载视频:', videoUrl)

        // 使用file-downloader云函数下载视频
        const downloadResult = await uniCloud.callFunction({
          name: 'file-downloader',
          data: {
            action: 'downloadFile',
            url: videoUrl,
            options: {
              maxSize: 500 * 1024 * 1024, // 500MB限制
              timeout: timeout
            }
          }
        })

        if (!downloadResult.result || !downloadResult.result.success) {
          const errorMsg = downloadResult.result?.error || '下载失败'
          console.error('云函数下载失败:', errorMsg)

          // 检查是否需要重新解析
          if (errorMsg.includes('不是视频文件') || errorMsg.includes('链接已失效') || errorMsg.includes('状态码: 403') || errorMsg.includes('状态码: 404')) {
            if (skipReparse) {
              throw new Error(errorMsg)
            } else {
              throw new Error('NEED_REPARSE:' + errorMsg)
            }
          }

          throw new Error(errorMsg)
        }

        const { data: base64Data, size } = downloadResult.result

        // 将base64数据写入临时文件
        const tempFilePath = `${wx.env.USER_DATA_PATH}/video_${Date.now()}.mp4`
        const fs = wx.getFileSystemManager()

        // 移除base64前缀
        const pureBase64 = base64Data.replace(/^data:[^;]+;base64,/, '')

        fs.writeFileSync(tempFilePath, pureBase64, 'base64')

        console.log('视频下载成功，文件大小:', size, 'bytes, 路径:', tempFilePath)

        return tempFilePath

      } catch (error) {
        console.error(`视频下载失败 (第${retryCount + 1}次):`, error)

        // 如果还有重试次数，等待一下再重试
        if (retryCount < maxRetries && !error.message.startsWith('NEED_REPARSE:')) {
          // 随机延迟1-3秒，避免被识别为机器人
          const delay = 1000 + Math.random() * 2000
          console.log(`等待${Math.round(delay/1000)}秒后进行第${retryCount + 2}次重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
          return await this.downloadVideo(videoUrl, retryCount + 1, skipReparse)
        } else {
          throw error
        }
      }


    },



    // 加载文件大小（使用云函数）
    async loadFileSize() {
      if (!this.resultData.processedData || !this.resultData.processedData.isUrl) {
        return
      }

      // 添加加载状态，避免重复请求
      if (this.loadingSizePromise) {
        return this.loadingSizePromise
      }

      this.loadingSizePromise = this.checkVideoSize(this.resultData.processedData.data)

      try {
        const size = await this.loadingSizePromise
        if (size > 0) {
          this.cachedFileSize = size
          console.log('[文件大小] 视频文件大小已缓存:', this.formatFileSize(size))

          // 触发界面更新
          this.$forceUpdate()
        } else {
          console.log('[文件大小] 无法获取文件大小，将显示未知')
        }
      } catch (error) {
        console.log('[文件大小] 加载文件大小失败:', error)
      } finally {
        this.loadingSizePromise = null
      }
    },

    // 检查视频文件大小（使用云函数）
    async checkVideoSize(videoUrl) {
      try {
        // 检查参数是否有效
        if (!videoUrl || typeof videoUrl !== 'string') {
          console.log('[文件大小] 无效的URL参数，跳过检查')
          return 0
        }

        // 检查缓存
        if (this.cachedFileSize > 0) {
          console.log(`[文件大小] 使用缓存：${this.formatFileSize(this.cachedFileSize)}`)
          return this.cachedFileSize
        }

        console.log('[文件大小] 开始使用云函数获取文件大小...')

        // 使用file-downloader云函数获取文件信息
        const infoResult = await uniCloud.callFunction({
          name: 'file-downloader',
          data: {
            action: 'getFileInfo',
            url: videoUrl,
            options: {
              timeout: 15000
            }
          }
        })

        console.log('[文件大小] 云函数返回结果:', infoResult)

        if (infoResult.result && infoResult.result.success && infoResult.result.size > 0) {
          const size = infoResult.result.size
          this.cachedFileSize = size
          console.log(`[文件大小] 获取成功：${this.formatFileSize(size)}`)
          return size
        } else {
          console.log('[文件大小] 云函数获取失败，详细结果:', infoResult.result)
          return 0
        }

      } catch (error) {
        console.log('[文件大小] 获取过程出错:', error)
        return 0 // 如果获取失败，返回0，不阻止下载
      }
    },



    // 处理可能需要代理的URL（目前直接返回原始URL）
    maybeProxyUrl(originalUrl) {
      // 由于已禁用代理服务，直接返回原始URL
      // 微博的视频和Live Photo在真机环境可以直接访问
      return originalUrl;
    },

    // 操作广告完成回调
    onOperationAdComplete(result) {
      console.log('操作广告完成:', result)
    },
    
    // 结果页广告关闭（现在通过配置控制，不再需要手动关闭）
    closeResultAd() {
      uni.showToast({
        title: '广告控制已集中管理',
        icon: 'none'
      })
      console.log('[广告] 结果页广告现在通过全局配置控制，不支持单独关闭')
    },
    
    // 结果页广告点击
    onResultAdClick() {
      console.log('结果页广告被点击')
      // 这里可以添加广告点击统计或其他逻辑
    },

    // 跳转到教程页面的常见问题章节
    goToTutorialDownloadFailed() {
      uni.navigateTo({
        url: '/pages/tutorial/index?section=faq'
      })
    },

    // 根据平台设置保存路径
    setSavedPath(type) {
      try {
        const systemInfo = uni.getSystemInfoSync()
        const platform = systemInfo.platform
        console.log('设置保存路径 - 平台:', platform, '类型:', type)

        let technicalPath = ''
        let friendlyDescription = ''

        // 根据平台设置路径
        if (platform === 'android') {
          technicalPath = '/storage/emulated/0/DCIM/Camera'
          if (type === 'video') {
            friendlyDescription = '手机相册 → 相机 → 视频'
          } else if (type === 'image') {
            friendlyDescription = '手机相册 → 相机 → 图片'
          } else {
            friendlyDescription = '手机相册 → 相机'
          }
        } else if (platform === 'ios') {
          technicalPath = '/var/mobile/Media/DCIM/Camera'
          if (type === 'video') {
            friendlyDescription = '照片 App → 相机胶卷 → 视频'
          } else if (type === 'image') {
            friendlyDescription = '照片 App → 相机胶卷 → 图片'
          } else {
            friendlyDescription = '照片 App → 相机胶卷'
          }
        } else {
          // 其他平台或开发工具 - 模拟Android路径
          technicalPath = '/storage/emulated/0/DCIM/Camera'
          if (type === 'video') {
            friendlyDescription = '手机相册 → 相机 → 视频'
          } else if (type === 'image') {
            friendlyDescription = '手机相册 → 相机 → 图片'
          } else {
            friendlyDescription = '手机相册 → 相机'
          }
        }

        this.savedPath = technicalPath
        this.friendlyPath = friendlyDescription
        console.log('友好路径:', this.friendlyPath)
        console.log('技术路径:', this.savedPath)

      } catch (error) {
        console.error('获取系统信息失败:', error)
        // 降级处理
        if (type === 'video') {
          this.savedPath = '/storage/emulated/0/DCIM/Camera'
          this.friendlyPath = '手机相册 → 相机 → 视频'
        } else if (type === 'image') {
          this.savedPath = '/storage/emulated/0/DCIM/Camera'
          this.friendlyPath = '手机相册 → 相机 → 图片'
        } else {
          this.savedPath = '/storage/emulated/0/DCIM/Camera'
          this.friendlyPath = '手机相册 → 相机'
        }
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F5F5;
}

/* 结果页广告位样式 */
.result-ad-placeholder {
  background: #FFFFFF;
  margin: 20rpx 0;  /* 左右无空白，上下有间距 */
  border-radius: 16rpx;  /* 圆角设计 */
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #E0E0E0;
  width: 100%;  /* 确保宽度一致 */
  box-sizing: border-box;
}

.result-ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.result-ad-label {
  font-size: 20rpx;
  color: #999;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.result-ad-close {
  font-size: 24rpx;
  color: #999;
  cursor: pointer;
  padding: 4rpx;
}

.result-ad-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  min-height: 80rpx;
}

.result-ad-thumbnail {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.result-ad-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.result-ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.result-ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-ad-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.result-ad-source {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.result-ad-source-icon {
  font-size: 20rpx;
  color: #999;
}

.result-ad-source-text {
  font-size: 20rpx;
  color: #999;
}

.result-ad-action-btn {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  flex-shrink: 0;
}

/* 广告位容器统一样式 */
.result-ad-placeholder {
  margin-left: 0;
  margin-right: 0;
  padding-left: 24rpx;
  padding-right: 24rpx;
}

/* 水印处理说明样式 */
.watermark-notice {
  background: rgba(255, 152, 0, 0.1);
  border: 1rpx solid rgba(255, 152, 0, 0.3);
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 24rpx;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.notice-icon {
  font-size: 28rpx;
  color: #FF9800;
}

.notice-title {
  font-size: 28rpx;
  color: #E65100;
  font-weight: 600;
}

.notice-content {
  font-size: 24rpx;
  color: #BF360C;
  line-height: 1.5;
}

/* 自定义Loading样式 */
.custom-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.custom-loading-content {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  padding: 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 400rpx;
  max-width: 600rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #ffffff;
  font-size: 32rpx;
  text-align: center;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
}



.video-section {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.video-container {
  position: relative;
}

.bilibili-warning {
  background: #E3F2FD;
  border: 1rpx solid #BBDEFB;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.large-file-warning {
  background: linear-gradient(135deg, #FFF9E6 0%, #FFF3CD 100%);
  border: 1rpx solid #FFE082;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.15);
}

.warning-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.warning-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  line-height: 1;
}

.warning-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF8F00;
  line-height: 1.2;
}

.warning-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.warning-text {
  font-size: 26rpx;
  color: #E65100;
  line-height: 1.4;
  font-weight: 500;
}

.warning-suggestion {
  font-size: 24rpx;
  color: #F57C00;
  line-height: 1.4;
  opacity: 0.9;
}

.warning-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}

.warning-text {
  font-size: 24rpx;
  color: #856404;
  flex: 1;
  line-height: 1.4;
}

/* 大文件警告中的warning-icon和warning-text样式优先级更高 */
.large-file-warning .warning-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  line-height: 1;
}

.large-file-warning .warning-text {
  font-size: 26rpx;
  color: #E65100;
  line-height: 1.4;
  font-weight: 500;
}

.bilibili-warning .warning-text {
  color: #1976D2;
}

.main-video {
  width: 100%;
  height: 500rpx;
  border-radius: 12rpx;
  background: #000;
}

.image-content {
  width: 100%;
}

.main-image-container {
  position: relative;
  width: 100%;
  height: 500rpx; /* 固定高度，防止布局抖动 */
  border-radius: 12rpx;
  overflow: hidden;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #F5F5F5;
  transition: opacity 0.3s ease;
}

.main-image.image-hidden {
  opacity: 0;
}

.image-loading-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  z-index: 1;
}

.loading-spinner-small {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5E5;
  border-top: 4rpx solid #E1251B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text-small {
  font-size: 24rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-live-photo-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.main-live-photo-container {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 25rpx;
  padding: 6rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.main-live-photo-btn {
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  border-radius: 20rpx;
}

.main-live-copy-btn {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 18rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid rgba(0, 122, 255, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.main-copy-link-btn {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
  z-index: 2;
}

.main-copy-icon {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
}

.main-copy-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.main-live-copy-btn .main-copy-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  color: #E1251B;
  line-height: 1;
}

/* 移除文字样式，新设计只显示图标 */

.main-play-icon {
  width: 24rpx;
  height: 24rpx;
  color: #ffffff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-live-text {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.main-image-info {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-counter {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.live-indicator {
  background: rgba(255, 107, 129, 0.9);
  color: #ffffff;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  backdrop-filter: blur(10rpx);
}

.image-gallery {
  margin-top: 30rpx;
}

.gallery-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.gallery-scroll {
  white-space: nowrap;
  height: 200rpx;
}

.gallery-item {
  display: inline-block;
  margin-right: 20rpx;
  width: 160rpx;
  height: 160rpx;
}

.live-photo-count-inline {
  color: #E1251B;
  font-size: 26rpx;
  margin-left: 10rpx;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.gallery-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background: #F5F5F5;
}

.live-photo-play-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  backdrop-filter: blur(10rpx);
}


.play-icon-small {
  width: 20rpx;
  height: 20rpx;
  color: #ffffff;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.live-text {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.image-index {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 30rpx;
  text-align: center;
}

.selected-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: #E1251B;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.image-container.active {
  transform: scale(0.95);
  opacity: 0.8;
}

.image-container.active .gallery-image {
  border: 4rpx solid #E1251B;
}

/* Live Photo 播放器样式 */
.live-photo-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.live-photo-player-container {
  width: 100%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  max-height: 80vh;
}

.live-photo-player-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E5E5E5;
}

.live-photo-player-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.live-photo-player-close {
  width: 60rpx;
  height: 60rpx;
  background: #E5E5E5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  cursor: pointer;
}

.live-photo-player-video {
  width: 100%;
  height: 400rpx;
  background: #000000;
}

.live-photo-player-actions {
  padding: 30rpx;
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.live-photo-player-btn {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  height: 72rpx;
}

.live-photo-player-btn.secondary {
  background: #F8F9FA;
  color: #333;
  border: 1rpx solid #E5E5E5;
}

.video-status {
  text-align: center;
  margin-top: 20rpx;
}

.status-text {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background: #F0F0F0;
  border-radius: 20rpx;
}

.action-section {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.action-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
}

.primary-btn {
  background: #ffffff;
  color: #E1251B;
  border: 2rpx solid #E1251B;
  box-shadow: 0 4rpx 12rpx rgba(225, 37, 27, 0.15);
}

.secondary-btn {
  background: #ffffff;
  color: #E1251B;
  border: 2rpx solid #E1251B;
  box-shadow: 0 4rpx 12rpx rgba(225, 37, 27, 0.15);
}

.share-btn {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  color: #ffffff;
  position: relative;
  flex-direction: row;
  height: 88rpx;
  min-height: 88rpx;
  padding: 0 20rpx;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.share-btn .btn-icon {
  font-size: 28rpx;
  margin-right: 0;
  margin-bottom: 0;
}

.share-btn .btn-text {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 0;
}

.help-btn {
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  color: #ffffff;
  position: relative;
  flex-direction: column;
  height: 88rpx;
  min-height: 88rpx;
  padding: 8rpx 20rpx;
  align-items: center;
  justify-content: center;
  gap: 2rpx;
  border: 2rpx solid rgba(255, 152, 0, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.2);
}

/* 新的保存失败按钮样式 */
.help-btn-new {
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  color: #ffffff;
  position: relative;
  flex-direction: row;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(255, 152, 0, 0.25);
  overflow: hidden;
  transition: all 0.3s ease;
}

.help-btn-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.help-btn-new .help-icon-container {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.help-btn-new .help-icon {
  font-size: 20rpx;
  position: relative;
  z-index: 2;
}

.help-btn-new .help-icon-shine {
  position: absolute;
  top: 6rpx;
  left: 6rpx;
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  z-index: 1;
}

.help-btn-new .help-text-container {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}

.help-btn-new .help-text {
  font-size: 26rpx;
  font-weight: 600;
  line-height: 1.2;
  color: #FFFFFF;
}

.help-btn-new .help-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  flex-shrink: 0;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

.help-btn-new .help-arrow-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  line-height: 1;
}

.help-btn .btn-icon {
  font-size: 24rpx;
  margin-right: 0;
  margin-bottom: 0;
}

.help-btn .btn-text {
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 0;
}

.help-btn .btn-hint {
  font-size: 20rpx;
  opacity: 0.9;
  font-weight: 400;
}

/* 旧样式已删除，使用新的help-btn-new */

@keyframes sparkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 保存路径显示样式 */
.saved-path-container {
  margin-top: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(240, 147, 251, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(240, 147, 251, 0.2);
}

.saved-path-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.saved-path-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.saved-path-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #ffffff;
}

.saved-path-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  word-break: break-all;
}

/* 简化的保存路径样式 */
.saved-path-friendly {
  margin-bottom: 12rpx;
}

.friendly-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.4;
}

.saved-path-detail {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  margin-top: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  flex-shrink: 0;
}

.detail-path {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.4;
  word-break: break-all;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.btn-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

.process-info {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-value.success {
  color: #34C759;
}

.info-value.error {
  color: #FF3B30;
}

.disclaimer {
  background: #FFF2F2;
  border: 1rpx solid #FFB3B3;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.disclaimer-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #D32F2F;
  display: block;
  margin-bottom: 15rpx;
}

.disclaimer-text {
  font-size: 24rpx;
  color: #D32F2F;
  line-height: 1.8;
}

/* 自定义大文件提醒弹窗样式 */
.large-file-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.large-file-dialog {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(50rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-header {
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.dialog-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
  position: relative;
  z-index: 1;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.dialog-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.dialog-main-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.dialog-sub-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: block;
}

.dialog-tip-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  background: #F8F9FA;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #FFC107;
  text-align: left;
  display: block;
}

.dialog-actions {
  display: flex;
  border-top: 1rpx solid #E5E5E5;
}

.dialog-btn {
  flex: 1;
  height: 100rpx;
  border: none;
  background: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

.dialog-btn:active {
  background: #F5F5F5;
}

.cancel-btn {
  border-right: 1rpx solid #E5E5E5;
  color: #666;
}

.cancel-btn .btn-text {
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  color: #ffffff;
}

.confirm-btn:active {
  background: linear-gradient(135deg, #C41E3A 0%, #E1251B 100%);
}

.confirm-btn .btn-text {
  color: #ffffff;
  font-weight: 600;
}

.btn-text {
  font-size: 28rpx;
}
</style>