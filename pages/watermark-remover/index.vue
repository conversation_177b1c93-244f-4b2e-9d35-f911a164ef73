<template>
  <view class="container">
    <!-- 动态倒计时弹窗 -->
    <view v-if="showCountdownDialog" class="countdown-overlay">
      <view class="countdown-dialog">
        <view class="countdown-title">请稍等</view>
        <view class="countdown-content">
          同一视频需要等待 {{ countdownSeconds }} 秒才能重新解析
        </view>
        <view class="countdown-button" @click="hideCountdownDialog">
          知道了
        </view>
      </view>
    </view>

    <!-- 顶部广告位 -->
    <view class="top-ad-placeholder" v-if="shouldShowTopAd">
      <view class="ad-header">
        <text class="ad-label">📺 广告</text>
      </view>
      <view class="ad-content">
        <view class="ad-thumbnail">
          <text class="ad-icon">🚀</text>
        </view>
        <view class="ad-info">
          <text class="ad-title">{{ appName }}工具</text>
          <text class="ad-desc">{{ appDescription }}</text>
          <view class="ad-source">
            <text class="ad-source-icon">🔧</text>
            <text class="ad-source-text">{{ appName }}</text>
          </view>
        </view>
        <button class="ad-action-btn" @click="onTopAdClick">立即使用</button>
      </view>
    </view>

    <!-- 顶部标题区域 -->
    <view class="header">
      <text class="title">{{ appName }}</text>
      <text class="subtitle">{{ appSlogan }}</text>
    </view>

    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-header">
        <text class="input-title">🔗 粘贴分享链接</text>
        <text class="clear-btn" @click="clearInput" v-if="linkInput">清空</text>
      </view>
      
      <view class="input-container">
        <textarea
          v-model="linkInput"
          placeholder="请粘贴视频/动态图文/文案的分享链接，支持主流短视频/音乐平台内容解析..."
          class="link-input"
          :maxlength="500"
          auto-height
        />
      </view>
      
      <!-- 功能开关 -->
      <view class="switches">
        <view class="switch-item">
          <text class="switch-label">记录历史</text>
          <switch :checked="saveHistory" @change="onSaveHistoryChange" color="#E1251B" />
        </view>
        <view class="switch-item">
          <text class="switch-label">自动粘贴</text>
          <switch :checked="autoPaste" @change="onAutoPasteChange" color="#E1251B" />
        </view>
      </view>
    </view>

    <!-- 移除了分割线和文件选择区域 -->

    <!-- 提取按钮 -->
    <view class="action-section">
      <button
        class="extract-btn"
        :class="{ 'disabled': !canExtract }"
        :disabled="!canExtract"
        @click="extractContent"
        :loading="isLoading"
      >
        <text class="btn-icon">✨</text>
        {{ isLoading ? '正在处理...' : 'Get无水印' }}
      </button>

      <!-- 分享推广按钮（使用微信官方推荐方式） -->
      <button 
        v-if="shouldShowShareCard" 
        class="share-btn-home" 
        open-type="share"
        @click="handleShareClick"
      >
        <text class="share-btn-icon">📤</text>
        <text class="share-btn-text">分享小程序给好友</text>
        <text class="share-btn-hint">获得24小时解析权限</text>
      </button>



      <!-- 使用教程链接 -->
      <view class="tutorial-link" @click="goToTutorial">
        <view class="tutorial-content">
          <view class="tutorial-icon">
            <text class="tutorial-icon-text">💡</text>
            <view class="tutorial-icon-shine"></view>
          </view>
          <view class="tutorial-info">
            <text class="tutorial-title">使用教程</text>
            <text class="tutorial-desc">所有的问题都在这里哦</text>
          </view>
          <view class="tutorial-arrow">
            <text class="arrow-text">查看</text>
          </view>
        </view>
      </view>

      <view class="tips">
        <text class="tips-text">ℹ️ 版权归平台及作者所有，本工具不储存任何内容</text>
      </view>

      <!-- 智能解析引擎广告位 -->
      <view class="ad-placeholder" v-if="shouldShowTopAd">
        <view class="ad-header">
          <text class="ad-label">🎯 智能解析引擎</text>
          <text class="ad-tip">AI识别水印位置，精准去除</text>
        </view>
        <view class="ad-content">
          <view class="ad-thumbnail">
            <text class="ad-icon">🤖</text>
          </view>
          <view class="ad-info">
            <text class="ad-title">AI智能识别</text>
            <text class="ad-desc">深度学习算法，精准定位水印</text>
            <view class="ad-source">
              <text class="ad-source-icon">🔬</text>
              <text class="ad-source-text">{{ appName }}</text>
            </view>
          </view>
          <button class="ad-action-btn" @click="onMiddleAdClick">立即体验</button>
        </view>
      </view>

      <!-- 功能说明 -->
      <view class="feature-info">
        <text class="feature-title">💡 功能说明</text>
        <view class="feature-list">
          <text class="feature-item">✅ 支持主流视频/音乐平台内容解析</text>
          <text class="feature-item">✅ 智能识别并提取高清视频内容</text>
          <text class="feature-item">✅ 一键保存到本地相册，便于使用</text>
        </view>
      </view>

      <!-- 免责声明 -->
      <view class="disclaimer">
        <text class="disclaimer-title">⚠️ 免责声明</text>
        <text class="disclaimer-text">本工具仅供个人学习交流使用，禁止用于任何商业或侵权用途。若因用户行为引发纠纷，本平台不承担任何责任。</text>
      </view>
    </view>

    <!-- 底部广告位已移除 -->

    <!-- 移除处理结果区域，用户已经在结果页面查看过了 -->
    
    <!-- 进入小程序广告组件 -->
    <EnterAd @complete="onEnterAdComplete" />

    <!-- 分享弹窗组件 -->
    <ShareDialog />

    <!-- 首次使用引导弹窗 - 已移到底部统一管理 -->







    <!-- 添加到我的小程序指示 -->
    <view v-if="showAddTip" class="add-tip-container" @click="hideAddTip">
      <!-- 指向三个点的箭头和文字 -->
      <view class="add-tip-pointer" :style="pointerStyle">
        <view class="pointer-content">
          <text class="pointer-text">{{ addTipConfig?.content?.mainText || '点击这里' }}</text>
          <text class="pointer-subtext">{{ addTipConfig?.content?.subText || '添加到我的小程序' }}</text>
        </view>
        <view class="pointer-arrow">{{ addTipConfig?.content?.arrow || '↗' }}</view>
      </view>
    </view>

    <!-- 弹窗组件 -->
    <EnterAd ref="enterAd" />
    <ShareDialog ref="shareDialog" />
    <FirstUseDialog
      :visible="showFirstUseDialog"
      :title="firstUseDialogTitle"
      @confirm="onFirstUseDialogConfirm"
      @close="showFirstUseDialog = false"
    />
    
    <!-- 自定义解析失败弹窗 -->
    <view v-if="showParseFailDialog" class="parse-fail-modal-overlay" @click="hideParseFailDialog">
      <view class="parse-fail-modal" @click.stop>
        <!-- 弹窗头部 -->
        <view class="parse-fail-header">
          <view class="parse-fail-icon">❌</view>
          <text class="parse-fail-title">解析失败</text>
          <view class="parse-fail-close" @click="hideParseFailDialog">✕</view>
        </view>
        
        <!-- 弹窗内容 -->
        <view class="parse-fail-content">
          <text class="parse-fail-subtitle">可能的原因：</text>
          <view class="parse-fail-reasons">
            <view class="parse-fail-reason">
              <text class="reason-number">1</text>
              <text class="reason-text">作品(或账号)设置成了私密</text>
            </view>
            <view class="parse-fail-reason">
              <text class="reason-number">2</text>
              <text class="reason-text">作品已经被删除了</text>
            </view>
            <view class="parse-fail-reason">
              <text class="reason-number">3</text>
              <text class="reason-text">作品还未审核通过</text>
            </view>
            <view class="parse-fail-reason">
              <text class="reason-number">4</text>
              <text class="reason-text">网络原因，请重试</text>
            </view>
          </view>
        </view>
        
        <!-- 弹窗按钮 -->
        <view class="parse-fail-footer">
          <button class="parse-fail-btn cancel-btn" @click="hideParseFailDialog">知道了</button>
          <button class="parse-fail-btn retry-btn" @click="retryParsing">重试</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import adManager from '../../components/ad-config.js'
import shareManager from '../../components/share-config.js'
import addTipManager from '../../components/add-tip-config.js'
import firstUseManager from '../../components/first-use-manager.js'
import appConfig from '../../components/app-config.js'
import EnterAd from '../../components/enter-ad/enter-ad.vue'
import ShareDialog from '../../components/share-dialog/share-dialog.vue'

import FirstUseDialog from '../../components/first-use-dialog/first-use-dialog.vue'


export default {
  components: {
    EnterAd,
    ShareDialog,
    FirstUseDialog
  },
  data() {
    return {
      linkInput: '',           // 链接输入
      processedData: null,     // 处理后的数据
      isLoading: false,        // 加载状态
      saveHistory: true,       // 是否保存历史
      autoPaste: false,        // 是否自动粘贴
      showCountdownDialog: false,  // 显示倒计时弹窗
      countdownSeconds: 0,     // 倒计时秒数
      countdownTimer: null,    // 倒计时定时器
      showAddTip: false,       // 显示添加到我的小程序提示
      addTipConfig: null,      // 添加提示配置

      // 应用信息
      appName: appConfig.getAppName(),
      appDescription: appConfig.getAppDescription(),
      appSlogan: appConfig.getAppSlogan(),
      isAppStart: false,       // 是否是应用启动（删除微信进程后重新进入）
      isFromBackground: false, // 是否从后台切换回来
      showFirstUseDialog: false,  // 显示首次使用引导弹窗
      firstUseDialogTitle: '',    // 首次使用弹窗标题
      firstUseDialogCallback: null, // 首次使用弹窗回调
      
      // 自定义解析失败弹窗状态
      showParseFailDialog: false,  // 显示解析失败弹窗
      parseFailMessage: '',        // 解析失败消息
      parseFailType: 'general',    // 失败类型：general或author
      


    }
  },
  
  computed: {
    canExtract() {
      return this.linkInput.trim() && !this.isLoading
    },

    // 🔧 检查是否显示首页广告
    shouldShowTopAd() {
      return adManager.shouldShowAd('result') // 复用result广告类型
    },

    // 🔧 检查是否显示分享卡片
    shouldShowShareCard() {
      return shareManager.shouldShowShare('profilePage') // 复用个人页面的分享配置
    },

    // 动态指示器位置样式
    pointerStyle() {
      if (!this.addTipConfig?.position?.pointer) return {}
      const pos = this.addTipConfig.position.pointer
      return {
        top: pos.top,
        right: pos.right
      }
    },


  },
  
  onLoad() {
    console.log('📄 [页面] onLoad - 页面加载')
    // 记录app启动时间（用于测试模式权限重置）
    uni.setStorageSync('app_start_time', Date.now())

    this.loadSettings()
    this.initAddTipConfig()
    console.log('[页面加载] 自动粘贴设置:', this.autoPaste)
    
    // 📝 测试云函数连接
    this.testCloudFunction()

    // 🔧 确保自动粘贴功能不受广告插件影响
    if (this.autoPaste) {
      // 延迟一点执行，确保页面完全加载
      setTimeout(() => {
        this.autoGetClipboard()
      }, 100)
    }

    // 监听历史页面的重新处理事件
    uni.$on('reprocessItem', (item) => {
      if (item.originalLink) {
        this.linkInput = item.originalLink
      }
    })

    // 监听首次使用弹窗显示事件
    uni.$on('showFirstUseDialog', (data) => {
      // 检查 addTipConfig 首次使用确认框开关
      if (!addTipManager.shouldShowFirstUseDialog()) {
        console.log('🏷️ [页面] addTipConfig 首次使用确认框已禁用，跳过弹窗事件')
        return
      }

      this.firstUseDialogTitle = data.title
      this.firstUseDialogCallback = data.onConfirm
      this.showFirstUseDialog = true
    })

    // 监听显示浮动提示事件
    uni.$on('showAddTip', (data) => {
      // 检查 addTipConfig 悬浮提示开关
      if (!addTipManager.shouldShowFloatingTip()) {
        console.log('🏷️ [页面] addTipConfig 悬浮提示已禁用，跳过浮动提示事件')
        return
      }

      this.showAddTip = true
      this.addTipConfig = {
        ...this.addTipConfig,
        duration: data.duration || 10000
      }

      // 自动隐藏
      setTimeout(() => {
        this.showAddTip = false
      }, data.duration || 10000)
    })

    // 🎯 监听App.vue发送的三个事件
    uni.$on('firstTimeUseEvent', (data) => {
      console.log('📄 [页面] 收到第一次使用事件', data)
      this.handleFirstTimeUse()
    })

    uni.$on('reenterAppEvent', (data) => {
      console.log('📄 [页面] 收到重新进入小程序事件', data)
      this.handleReenterApp(data)
    })

    uni.$on('appSwitchEvent', (data) => {
      console.log('📄 [页面] 收到应用切换事件', data)
      this.handleAppSwitch(data)
    })

    // 🎯 第一次使用检查现在由 App.vue 统一处理

    // 🎯 第一次使用检查现在由 App.vue 统一处理
  },

  onShow() {
    console.log('👁️ [页面] onShow - 页面显示')
    console.log('[页面显示] 自动粘贴设置:', this.autoPaste)

    // 🔧 确保自动粘贴功能不受广告插件影响
    if (this.autoPaste) {
      // 延迟一点执行，避免与其他功能冲突
      setTimeout(() => {
        this.autoGetClipboard()
      }, 200)
    }
  },

  onHide() {
    // 记录页面隐藏时间，用于判断是否是真正的应用启动
    uni.setStorageSync('app_last_exit_time', Date.now())
    console.log('[页面隐藏] 记录退出时间')
  },

  onUnload() {
    // 移除事件监听
    uni.$off('reprocessItem')
    uni.$off('firstTimeUseEvent')
    uni.$off('reenterAppEvent')
    uni.$off('appSwitchEvent')

    // 清理倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },

  // ⭐ 添加微信小程序分享配置
  onShareAppMessage(res) {
    console.log('[分享] 触发微信分享', res)
    console.log('[分享] 分享触发来源:', res.from) // button（按钮触发）或 menu（右上角菜单触发）
    
    // 定义分享内容
    const shareInfo = {
      // 分享标题（包含吸引力的文案）
      title: '超好用的去水印工具，快来试试！',
      
      // 分享路径（点击分享卡片后打开的首页）
      path: '/pages/watermark-remover/index',
      
      // 分享卡片的图片（建议尺寸：500*400px）
      imageUrl: '/static/share-card.jpg',
      
      // 分享成功的回调
      success: (res) => {
        console.log('[分享] 分享成功', res)
        
        // 🎁 调用分享成功处理，获得24小时权限
        shareManager.handleShareSuccess({
          success: true,
          shareType: 'wechatFriend',
          source: 'homePage',
          timestamp: Date.now()
        })
        
        // 显示分享成功提示（增强版）
        uni.showModal({
          title: '分享成功！',
          content: '恭喜您获得24小时免费解析权限！现在可以无限制解析所有平台视频了~',
          showCancel: false,
          confirmText: '开始解析',
          success: () => {
            console.log('[分享] 用户确认24小时权限提示')
          }
        })
      },
      
      // 分享失败的回调（包括用户取消分享）
      fail: (res) => {
        console.log('[分享] 分享失败', res)
        
        // 如果是用户取消，不提示；其他错误才提示
        if (res.errMsg !== 'shareAppMessage:fail cancel') {
          uni.showToast({
            title: '分享失败，请重试',
            icon: 'none',
            duration: 2000
          })
        } else {
          console.log('[分享] 用户取消分享')
        }
      }
    }
    
    return shareInfo
  },
  
  methods: {
    // 📝 测试云函数连接
    async testCloudFunction() {
      try {
        const testResult = await uniCloud.callFunction({
          name: 'unified-parser',
          data: {
            test: true
          }
        })

        console.log('✅ 云函数连接成功!', testResult)

      } catch (error) {
        console.error('❌ 云函数连接失败:', error)
      }
    },



    // 🎯 初始化添加提示配置
    initAddTipConfig() {
      this.addTipConfig = addTipManager.getConfig()
      console.log('🏷️ [页面] 初始化添加提示配置', this.addTipConfig)
    },

    // 🎯 第一次使用事件处理
    handleFirstTimeUse() {
      console.log('🆕 [页面] 处理第一次使用事件')

      // 1. 检查是否应该显示添加常用小程序确认框
      if (addTipManager.shouldShowFirstUseDialog()) {
        console.log('🆕 [页面] 显示添加常用小程序确认框')
        setTimeout(() => {
          this.firstUseDialogTitle = '下次可以这样找到我'
          this.showFirstUseDialog = true
        }, 500)
      } else {
        console.log('🆕 [页面] addTipConfig 首次使用确认框已禁用，跳过显示')

        // 2. 如果添加确认框被禁用，直接显示教程推荐
        this.showTutorialRecommendationIfNeeded()
      }
    },

    // 🎯 重新进入小程序事件处理
    handleReenterApp(data) {
      console.log('🔄 [页面] 处理重新进入小程序事件', data)

      // 1. 显示弹窗广告
      setTimeout(() => {
        console.log('🔄 [页面] 显示重新进入广告')
        this.showEnterAd()
      }, 500)

      // 2. 显示浮动添加提示
      setTimeout(() => {
        console.log('🔄 [页面] 显示浮动添加提示')
        this.showAddTipFloat()
      }, 1500)
    },

    // 🎯 应用切换事件处理
    handleAppSwitch(data) {
      console.log('🔀 [页面] 处理应用切换事件', data)

      // 只显示弹窗广告
      setTimeout(() => {
        console.log('🔀 [页面] 显示应用切换广告')
        this.showEnterAd()
      }, 500)
    },

    // 🎯 显示弹窗广告
    showEnterAd() {
      // 检查广告总开关
      if (!adManager.shouldShowAd('enter')) {
        console.log('📺 [页面] AD_CONFIG.globalEnabled = false 或进入广告已禁用，跳过进入广告')
        return
      }

      console.log('📺 [页面] 显示弹窗广告')
      // 触发进入广告组件显示
      this.$refs.enterAd && this.$refs.enterAd.showEnterAd()
    },

    // 🎯 显示浮动添加提示
    showAddTipFloat() {
      // 检查 addTipConfig 悬浮提示开关
      if (!addTipManager.shouldShowFloatingTip()) {
        console.log('🏷️ [页面] addTipConfig 悬浮提示已禁用，跳过浮动提示')
        return
      }

      console.log('🏷️ [页面] 显示浮动添加提示')
      this.showAddTip = true

      // 获取配置的显示时长
      const config = addTipManager.getConfig()
      const duration = config.features.floatingTip.duration || 20000

      // 自动隐藏
      setTimeout(() => {
        this.showAddTip = false
      }, duration)
    },

    // 🎯 首次使用弹窗确认回调
    onFirstUseDialogConfirm() {
      console.log('🆕 [页面] 用户确认了首次使用弹窗')
      this.showFirstUseDialog = false

      // 显示教程推荐弹窗
      setTimeout(() => {
        this.showTutorialRecommendationIfNeeded()
      }, 500)
    },

    // 💬 显示添加到常用小程序确认框
    showAddToMiniProgramDialog() {
      return new Promise((resolve) => {
        this.firstUseDialogTitle = '下次可以这样找到我'
        this.showFirstUseDialog = true
        this.firstUseDialogCallback = () => {
          this.showFirstUseDialog = false
          resolve(true)
        }
      })
    },

    // 📚 显示教程推荐弹窗
    showTutorialDialog() {
      return new Promise((resolve) => {
        uni.showModal({
          title: '新手教程',
          content: '这是您第一次使用，是否需要查看使用教程？\n教程将帮助您快速了解如何使用去水印功能。',
          confirmText: '查看教程',
          cancelText: '跳过',
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    },



    // 显示频率限制动态倒计时弹窗
    showRateLimitModal(errorMessage) {
      console.log('显示频率限制弹窗:', errorMessage)

      // 提取剩余秒数
      const match = errorMessage.match(/等待\s*(\d+)\s*秒/)
      if (!match) {
        console.log('无法提取秒数，显示静态弹窗')
        // 如果无法提取秒数，显示原始消息
        uni.showModal({
          title: '请稍等',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        })
        return
      }

      let remainingSeconds = parseInt(match[1])
      console.log('提取到剩余秒数:', remainingSeconds)

      // 使用自定义弹窗实现动态倒计时
      this.startCountdown(remainingSeconds)
    },

    // 开始倒计时
    startCountdown(seconds) {
      // 清除之前的定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }

      // 设置初始值并显示弹窗
      this.countdownSeconds = seconds
      this.showCountdownDialog = true

      // 开始倒计时
      this.countdownTimer = setInterval(() => {
        this.countdownSeconds--

        if (this.countdownSeconds <= 0) {
          // 倒计时结束，自动关闭弹窗
          this.hideCountdownDialog()
        }
      }, 1000)
    },

    // 隐藏倒计时弹窗
    hideCountdownDialog() {
      this.showCountdownDialog = false

      // 清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
    },

    // 显示自定义解析失败弹窗
    showParseFailModal(errorMessage) {
      // 判断失败类型
      const isAuthorError = errorMessage.includes('无法获取视频作者信息')
      
      this.parseFailType = isAuthorError ? 'author' : 'general'
      this.parseFailMessage = errorMessage
      this.showParseFailDialog = true
    },

    // 隐藏自定义解析失败弹窗
    hideParseFailDialog() {
      this.showParseFailDialog = false
      this.parseFailMessage = ''
      this.parseFailType = 'general'
    },

    // 重试解析
    async retryParsing() {
      // 隐藏失败弹窗
      this.hideParseFailDialog()
      
      // 如果输入框有内容，重新解析
      if (this.linkInput && this.linkInput.trim()) {
        await this.extractContent()
      } else {
        uni.showToast({
          title: '请先输入视频链接',
          icon: 'none'
        })
      }
    },

    // 进入广告完成回调
    onEnterAdComplete(result) {
      console.log('进入广告完成:', result)
    },
    
    // 顶部广告关闭
    closeTopAd() {
      this.showTopAd = false
    },
    
    // 顶部广告点击
    onTopAdClick() {
      console.log('顶部广告被点击')
      // 这里可以添加广告点击统计或其他逻辑
    },
    
    // 中间广告点击
    onMiddleAdClick() {
      console.log('中间广告被点击')
      // 这里可以添加广告点击统计或其他逻辑
    },
    
    // 清空输入
    clearInput() {
      this.linkInput = ''
    },
    
    // 保存历史开关
    onSaveHistoryChange(e) {
      this.saveHistory = e.detail.value
      this.saveSettings()
    },
    
    // 自动粘贴开关
    onAutoPasteChange(e) {
      this.autoPaste = e.detail.value
      console.log('[自动粘贴开关] 状态变更为:', this.autoPaste)

      this.saveSettings()

      // 🔧 当开启自动粘贴时，立即尝试获取剪贴板内容
      if (this.autoPaste) {
        setTimeout(() => {
          this.autoGetClipboard()
        }, 100)
      }
    },

    // 自动获取剪贴板（仅读取，不进行任何复制操作）
    autoGetClipboard() {
      console.log('[自动粘贴] 开始获取剪贴板内容')
      
      // 🔧 添加调试信息，帮助排查"内容已复制"问题
      const currentPages = getCurrentPages()
      const currentPage = currentPages[currentPages.length - 1]
      const currentRoute = currentPage ? currentPage.route : 'unknown'
      
      console.log('[自动粘贴] 当前页面路径:', currentRoute)
      
      // 如果当前不在主页面，跳过自动粘贴
      if (currentRoute !== 'pages/watermark-remover/index') {
        console.log('[自动粘贴] 不在主页面，跳过自动粘贴功能')
        return
      }
      
      // 重要：只使用 uni.getClipboardData 读取，绝不使用 uni.setClipboardData 复制
      uni.getClipboardData({
        success: (res) => {
          const data = res.data
          console.log('[自动粘贴] 剪贴板内容长度:', data ? data.length : 0)

          if (data && this.isValidLink(data) && data !== this.linkInput) {
            console.log('[自动粘贴] 检测到有效链接，自动填入')
            this.linkInput = data
            
            // 使用普通的提示，不涉及复制操作
            uni.showToast({
              title: '已自动填入链接',
              icon: 'success',
              duration: 1500
            })
          } else {
            console.log('[自动粘贴] 链接无效或与当前输入相同，跳过')
          }
        },
        fail: (err) => {
          console.log('[自动粘贴] 获取剪贴板失败:', err)
        }
      })
    },

    // 验证链接有效性 - 已移至统一解析器
    isValidLink(text) {
      // 简单的格式检查，详细验证由统一解析器处理
      if (!text || typeof text !== 'string') {
        return false
      }

      // 🔧 修复：更宽松的链接验证，支持更多平台链接格式
      // 检查是否包含 http/https 或者包含支持的平台域名
      const hasHttpProtocol = /^https?:\/\/.+/.test(text)
      const hasSupportedDomain = /douyin\.com|dy\.com|kuaishou\.com|ks\.com|v\.kuaishou\.com|kwai\.app|ks\.app|gifshow\.com|xiaohongshu\.com|xhslink\.com|bilibili\.com|b23\.tv|weibo\.com|weishi\.qq\.com|video\.weishi\.qq\.com|h5\.pipix\.com|qishui\.douyin\.com/i.test(text)

      return hasHttpProtocol || hasSupportedDomain
    },
    
    // 显示图片工具推荐
    showImageTools() {
      uni.showModal({
        title: '图片去水印工具推荐',
        content: `推荐使用以下专业工具：

1. cleanup.pictures
   - 完全免费的AI去水印工具
   - 网址：cleanup.pictures

2. remove.bg
   - 专业的背景移除工具
   - 网址：remove.bg

3. Adobe Photoshop
   - 使用"内容感知填充"功能
   - 专业级图像处理软件

4. GIMP (免费)
   - 开源图像编辑软件
   - 使用修复工具去除水印`,
        showCancel: true,
        cancelText: '知道了',
        confirmText: '复制链接',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: 'cleanup.pictures\nremove.bg',
              success: () => {
                uni.showToast({
                  title: '链接已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },
    
    // 提取内容
    async extractContent() {
      if (!this.canExtract) return

      // 🔒 隐私授权检查（微信小程序）
      // #ifdef MP-WEIXIN
      const privacyAuthorized = await this.checkPrivacyAuthorization()
      if (!privacyAuthorized) {
        console.log('🔒 [隐私] 用户拒绝了隐私授权，停止处理')
        return
      }
      // #endif

      console.log('开始提取内容...')
      this.isLoading = true

      try {
        let result
        if (this.linkInput.trim()) {
  
          // 处理链接
          result = await this.processLink(this.linkInput.trim())
        }

        // 验证解析结果质量
        if (!result || !result.author || result.author === '未知作者' || result.author === '抖音用户' || result.author.trim() === '') {
          throw new Error('解析失败：无法获取视频作者信息，可能是视频已被删除、设置为私密或链接无效')
        }

        // 保存历史记录
        if (this.saveHistory && result) {
          this.saveToHistory(result)
        }
        
        // 立即跳转到结果页面（不等待其他操作）
        uni.setStorageSync('temp_result', JSON.stringify(result))
        uni.navigateTo({
          url: '/pages/result/index',
          animationType: 'slide-in-right',
          animationDuration: 200 // 加快动画速度
        })

      } catch (error) {
        console.error('处理失败:', error)

        // 区分频率限制错误和真正的解析失败
        const errorMessage = error.message || '未知错误'
        console.log('错误消息:', errorMessage)

        // 检查是否为频率限制相关错误
        const isRateLimitError = errorMessage.includes('等待') ||
                                errorMessage.includes('请求过于频繁') ||
                                errorMessage.includes('同一视频') ||
                                errorMessage.includes('每分钟最多') ||
                                errorMessage.includes('每小时最多')

        console.log('是否为频率限制错误:', isRateLimitError)

        if (isRateLimitError) {
          // 频率限制错误，显示动态倒计时提示
          console.log('调用 showRateLimitModal')
          this.showRateLimitModal(errorMessage)
        } else {
          // 真正的解析失败 - 使用自定义弹窗
          this.showParseFailModal(errorMessage)
        }
      } finally {
        this.isLoading = false
      }
    },
    
    // 处理链接
    async processLink(link) {
      // 清理链接
      const cleanedLink = this.cleanLink(link)

      try {
        // 🛡️ 防恶意请求检查
        const rateLimitCheck = adManager.checkRateLimit(cleanedLink)
        if (!rateLimitCheck.allowed) {
          throw new Error(rateLimitCheck.message)
        }

        // 基本格式检查
        if (!this.isValidLink(cleanedLink)) {
          throw new Error('链接格式无效，请输入以 http:// 或 https:// 开头的链接')
        }

        // 📝 记录请求
        adManager.recordRequest(cleanedLink)

        // 调用统一解析器 - 自动检测平台并路由到对应解析器
        console.log('🚀 开始调用云函数:', {
          functionName: 'unified-parser',
          link: cleanedLink,
          timestamp: new Date().toISOString()
        })
        
        const result = await uniCloud.callFunction({
          name: 'unified-parser',
          data: {
            link: cleanedLink,
            options: {
              debug: true // 开启调试信息
            }
          }
        })
        
        console.log('✅ 云函数调用成功，原始结果:', result)
        console.log('📊 结果详情:', {
          hasResult: !!result.result,
          resultType: typeof result.result,
          resultKeys: result.result ? Object.keys(result.result) : [],
          errorInfo: result.errMsg || 'none'
        })



        // 检查返回结果
        if (result.result) {
          const data = result.result
          
          // 检查是否为错误结果
          if (data.type === 'error') {
            const errorMsg = data.content || data.error?.message || '解析失败'
            console.error('解析失败:', errorMsg)
            throw new Error(errorMsg)
          }
          
          // 保存原始链接用于重新处理
          data.originalLink = link
          return data
        } else {
          console.error('统一解析器返回空结果')
          throw new Error('解析失败：服务器返回空结果')
        }
      } catch (error) {
        console.error('🚫 云函数调用失败，详细错误信息:', {
          errorMessage: error.message,
          errorCode: error.code,
          errorType: error.errCode,
          fullError: error,
          timestamp: new Date().toISOString()
        })

        // 如果是云函数相关错误，提供模拟数据
        if (error.message && (error.message.includes('FunctionNotFound') ||
            error.message.includes('uniCloud') ||
            error.message.includes('云函数') ||
            error.errCode === 'FUNCTION_NOT_FOUND' ||
            error.code === 'FUNCTION_NOT_FOUND')) {
          console.log('🚨 检测到云函数未部署，返回模拟数据')
          
          // 显示提示信息
          uni.showToast({
            title: '云函数未部署，使用模拟数据',
            icon: 'none',
            duration: 3000
          })
          
          return this.getMockData(cleanedLink)
        }

        // 其他错误直接抛出
        throw new Error(error.message || '网络请求失败，请检查网络连接')
      }
    },

    // 获取模拟数据（用于测试）
    getMockData(link) {
      // 简单的平台检测用于模拟数据  
      let platform = 'douyin' // 默认
      if (/kuaishou\.com|ks\.com|v\.kuaishou\.com|kwai\.app|ks\.app|gifshow\.com/i.test(link)) {
        platform = 'kuaishou'
      } else if (/xiaohongshu\.com|xhslink\.com/i.test(link)) {
        platform = 'xiaohongshu'
      } else if (/bilibili\.com|b23\.tv|b23\.com/i.test(link)) {
        platform = 'bilibili'
      }

      // 模拟处理后的数据（base64格式）
      const mockVideoData = 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDE='
      const mockImageData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxAAPwCdABmX/9k='

      // 模拟不同平台的数据
      const mockData = {
        douyin: {
          title: '春梅饭馆活动来了，菜价本来都不贵，还有59抵100',
          author: '小新吃不饱',
          processedData: {
            data: mockVideoData,
            type: 'video/mp4',
            size: 1024000
          },
          type: 'video',
          platform: 'douyin',
          source: '抖音',
          originalLink: link,
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        },
        kuaishou: {
          title: '快手测试视频',
          author: '快手用户',
          processedData: {
            data: mockVideoData,
            type: 'video/mp4',
            size: 1024000
          },
          type: 'video',
          platform: 'kuaishou',
          source: '快手',
          originalLink: link,
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        },
        xiaohongshu: {
          title: '小红书美妆分享｜超好用的平价护肤品推荐',
          author: '美妆达人小仙女',
          processedData: [mockImageData, mockImageData, mockImageData], // 多张图片
          type: 'image',
          contentType: 'image',
          platform: 'xiaohongshu',
          source: '小红书',
          originalLink: link,
          isImageContent: true,
          imageUrls: [
            'https://example.com/image1.jpg',
            'https://example.com/image2.jpg',
            'https://example.com/image3.jpg'
          ],
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        },
        bilibili: {
          title: '【老司机必备】Deepseek与Grok的破甲指令，不撤回爽玩的最佳选择',
          author: 'B站UP主',
          processedData: {
            data: mockVideoData,
            type: 'video/mp4',
            size: 2048000,
            duration: 180,
            isDirectUrl: true
          },
          type: 'video',
          platform: 'bilibili',
          source: 'B站',
          originalLink: link,
          coverUrl: 'https://example.com/bilibili-cover.jpg',
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        }
      }

      return mockData[platform] || {
        title: '未知平台内容',
        author: '未知作者',
        processedData: {
          data: mockImageData,
          type: 'image/jpeg',
          size: 512000
        },
        type: 'image',
        platform: 'unknown',
        source: '未知平台',
        originalLink: link,
        note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
      }
    },


    // 清理链接
    cleanLink(link) {
      // 移除多余的空格和换行
      link = link.trim().replace(/\s+/g, ' ')

      // 提取URL部分
      const urlMatch = link.match(/(https?:\/\/[^\s]+)/)
      if (urlMatch) {
        return urlMatch[1]
      }

      // 如果没有找到完整URL，尝试提取域名相关部分
      const domainMatch = link.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/)
      if (domainMatch) {
        return 'https://' + domainMatch[1]
      }

      return link
    },
    
    // 移除了文件处理相关方法
    
    // 移除获取媒体源的方法，这些功能现在在结果页面

    // 移除保存相关方法，这些功能现在在结果页面



    // 移除所有保存和分享相关方法，这些功能现在在结果页面


    
    // 保存到历史记录（仅保存基本信息和原始链接，不保存解析结果）
    saveToHistory(data) {
      const history = uni.getStorageSync('watermark_history') || []
      
      // 安全的历史记录数据结构 - 只保存链接信息，不保存解析内容
      const item = {
        id: Date.now().toString(),
        timestamp: Date.now(),
        title: data.title || '未知标题',
        author: data.author || '未知作者',
        source: this.shouldShowSource() ? (data.source || '未知来源') : '短视频平台', // 可配置显示来源
        platform: data.platform || 'unknown',
        type: data.type || 'unknown', // 'video', 'image', 'text'
        contentType: this.getContentTypeText(data.type),
        originalLink: data.originalLink || '', // 只保存原始链接
        description: data.content ? (data.content.length > 30 ? data.content.substring(0, 30) + '...' : data.content) : '', // 只保存简短描述
        // 不再保存解析后的视频、图片链接等，强制用户重新解析
        hasLivePhoto: this.hasLivePhotoContent(data),
        livePhotoCount: this.getLivePhotoCount(data)
      }
      
      history.unshift(item)
      
      // 只保留最近100条记录
      if (history.length > 100) {
        history.splice(100)
      }
      
      uni.setStorageSync('watermark_history', history)
    },

    // 检查是否显示具体来源（可配置）
    shouldShowSource() {
      const settings = uni.getStorageSync('watermark_settings') || {}
      return settings.showSource !== false // 默认显示，可通过设置关闭
    },

    // 获取内容类型友好名称
    getContentTypeText(type) {
      const typeMap = {
        'video': '视频',
        'image': '图文',
        'text': '文本'
      }
      return typeMap[type] || '未知'
    },

    // 提取视频URL
    extractVideoUrl(data) {
      if (data.processedData?.isUrl && data.type === 'video') {
        return data.processedData.data
      }
      return ''
    },

    // 提取图片URLs
    extractImageUrls(data) {
      if (data.type === 'image' && data.processedData?.imageUrls) {
        return data.processedData.imageUrls.slice(0, 3) // 最多保存3个图片URL用于显示
      }
      return []
    },

    // 提取封面URL
    extractCoverUrl(data) {
      if (data.coverUrl) return data.coverUrl
      if (data.processedData?.coverUrl) return data.processedData.coverUrl
      // 从图片URL中取第一个作为封面
      if (data.processedData?.imageUrls && data.processedData.imageUrls.length > 0) {
        return data.processedData.imageUrls[0]
      }
      return ''
    },

    // 检查是否有Live Photo内容
    hasLivePhotoContent(data) {
      return !!(data.processedData?.livePhotoVideos && data.processedData.livePhotoVideos.length > 0)
    },

    // 获取Live Photo数量
    getLivePhotoCount(data) {
      if (data.processedData?.livePhotoVideos) {
        return data.processedData.livePhotoVideos.filter(video => video && video !== null).length
      }
      return 0
    },
    
    // 加载设置
    loadSettings() {
      const settings = uni.getStorageSync('watermark_settings') || {}
      this.saveHistory = settings.saveHistory !== false  // 默认开启历史记录
      this.autoPaste = settings.autoPaste !== false      // 🔧 修改：默认开启自动粘贴（用于测试隐私授权弹窗）
      
      console.log('[设置加载] 自动粘贴默认开启，当前状态:', this.autoPaste)
      console.log('[设置加载] 历史记录状态:', this.saveHistory)
    },

    // 保存设置
    saveSettings() {
      const settings = {
        saveHistory: this.saveHistory,
        autoPaste: this.autoPaste
      }
      uni.setStorageSync('watermark_settings', settings)
    },

    // 视频加载开始
    onVideoLoadStart() {
      this.videoStatus = '正在加载视频...'
      console.log('视频开始加载')
    },

    // 视频可以播放
    onVideoCanPlay() {
      this.videoStatus = '视频加载完成'
      console.log('视频可以播放')
      setTimeout(() => {
        this.videoStatus = ''
      }, 2000)
    },

    // 视频加载错误
    onVideoError(e) {
      console.error('视频加载失败:', e)
      this.videoStatus = '视频加载失败，可能是网络问题或视频格式不支持'

      // 显示错误提示
      uni.showModal({
        title: '视频播放失败',
        content: '视频无法播放，可能原因：\n1. 网络连接问题\n2. 视频格式不支持\n3. 文件过大\n\n建议：尝试保存到相册后播放',
        showCancel: false,
        confirmText: '我知道了'
      })
    },

    // 获取数据大小
    getDataSize(processedData) {
      if (!processedData || !processedData.data) {
        return '未知'
      }

      if (processedData.isUrl) {
        return 'URL格式'
      }

      // 计算base64数据大小
      const base64Data = processedData.data.replace(/^data:[^;]+;base64,/, '')
      const sizeInBytes = (base64Data.length * 3) / 4

      if (sizeInBytes < 1024) {
        return `${Math.round(sizeInBytes)} B`
      } else if (sizeInBytes < 1024 * 1024) {
        return `${Math.round(sizeInBytes / 1024)} KB`
      } else {
        return `${Math.round(sizeInBytes / (1024 * 1024))} MB`
      }
    },

    // 广告相关事件处理
    onAdClick(event) {
      console.log('广告被点击:', event)
      // 记录广告点击数据
      // 可以发送到统计服务
    },

    onAdClose(event) {
      console.log('广告被关闭:', event)
      // 记录广告关闭数据
    },

    onWelcomeComplete() {
      console.log('用户完成欢迎流程')
      // 可以记录用户激活数据
    },

    // 信息流广告事件处理
    onFeedAdClick(event) {
      console.log('信息流广告被点击:', event)
      // 记录广告点击数据
      // 可以发送到统计服务
    },

    onFeedAdClose(event) {
      console.log('信息流广告被关闭:', event)
      // 记录广告关闭数据
    },
    
    // 进入广告完成
    onEnterAdComplete(data) {
      console.log('进入广告完成:', data)
    },
    
    // 处理解析结果
    handleParseResult(data) {
      console.log('进入广告完成:', data)
    },

    // 🧪 重置为第一次使用（开发测试用）
    resetToFirstUse() {
      console.log('[测试] 重置为第一次使用状态')
      firstUseManager.resetFirstUseState()
      uni.showToast({
        title: '已重置为第一次使用',
        icon: 'success'
      })
    },

    // 显示分享弹窗
    showShareDialog() {
      console.log('[首页] 显示分享弹窗')
      shareManager.showShareDialog({
        entryType: 'profilePage',
        showRewardHint: true
      })
    },

    // 处理分享按钮点击（用于 open-type="share" 按钮）
    handleShareClick() {
      console.log('[首页] 分享按钮被点击')
      // 在微信小程序中，这里不需要手动处理，会自动调用 onShareAppMessage
    },

    // 处理首次使用弹窗确认 - 已删除重复方法，使用上方的 onFirstUseDialogConfirm

    // 显示教程推荐（如果需要的话）
    showTutorialRecommendationIfNeeded() {
      const hasShownTutorial = uni.getStorageSync('hasShownTutorial')

      if (!hasShownTutorial) {
        console.log('🆕 [页面] 显示教程推荐')

        uni.showModal({
          title: '新手教程',
          content: '这是您第一次使用，是否需要查看使用教程？\n教程将帮助您快速了解如何使用去水印功能。',
          confirmText: '查看教程',
          cancelText: '跳过',
          success: (res) => {
            if (res.confirm) {
              console.log('🆕 [页面] 用户选择查看教程，跳转到教程页面')
              uni.navigateTo({
                url: '/pages/tutorial/index'
              })
            } else {
              console.log('🆕 [页面] 用户跳过教程')
            }
          }
        })

        uni.setStorageSync('hasShownTutorial', true)
      } else {
        console.log('🆕 [页面] 教程推荐已显示过，跳过')
      }
    },

    // 显示教程推荐（保持向后兼容）
    showTutorialRecommendation() {
      this.showTutorialRecommendationIfNeeded()
    },













    // 隐藏添加到我的小程序提示
    hideAddTip() {
      this.showAddTip = false
      console.log('[添加提示] 提示已隐藏')
    },



    // 🔒 隐私授权检查（微信小程序）
    async checkPrivacyAuthorization() {
      // #ifdef MP-WEIXIN
      return new Promise((resolve) => {
        try {
          wx.getPrivacySetting({
            success: res => {
              console.log('🔒 [隐私] 隐私设置检查结果:', res)
              if (res.needAuthorization) {
                console.log('🔒 [隐私] 需要用户授权')
                // 微信会自动弹出官方隐私保护指引弹窗
                // 等待用户点击同意或拒绝
                wx.onNeedPrivacyAuthorization((resolve_privacy, eventInfo) => {
                  console.log('🔒 [隐私] 触发隐私授权事件:', eventInfo)
                  // 用户需要点击同意按钮，这里只是监听
                  // 具体的同意逼辑在微信官方弹窗中处理
                  this.privacyResolve = resolve_privacy
                  this.mainResolve = resolve
                })
                
                // 设置超时，防止用户不操作
                setTimeout(() => {
                  resolve(false)
                }, 30000) // 30秒超时
              } else {
                console.log('🔒 [隐私] 用户已同意隐私协议')
                resolve(true)
              }
            },
            fail: err => {
              console.error('🔒 [隐私] 隐私设置检查失败:', err)
              resolve(true) // 失败时默认允许继续
            }
          })
        } catch (error) {
          console.error('🔒 [隐私] 隐私检查异常:', error)
          resolve(true) // 异常时默认允许继续
        }
      })
      // #endif
      
      // 非微信小程序环境直接返回true
      // #ifndef MP-WEIXIN
      return true
      // #endif
    },

    // 跳转到使用教程
    goToTutorial() {
      uni.navigateTo({
        url: '/pages/tutorial/index'
      })
    },




  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 50%, #E1251B 100%);
  padding: 20rpx;
}

.header {
  text-align: center;
  padding: 60rpx 0 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.input-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.input-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 16rpx;
  flex: 1;
}

.clear-btn {
  font-size: 28rpx;
  color: #E1251B;
}

.input-container {
  margin-bottom: 40rpx;
}

.link-input {
  width: 100%;
  min-height: 180rpx;
  padding: 24rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #F8F9FA;
  box-sizing: border-box;
}

.switches {
  display: flex;
  justify-content: space-between;
}

.switch-item {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 30rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.file-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.file-options {
  display: flex;
  justify-content: space-around;
}

.file-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  border: 2rpx dashed #E5E5E5;
  border-radius: 16rpx;
  width: 280rpx;
}

.file-option.disabled {
  opacity: 0.6;
  border-color: #F0F0F0;
  background: #FAFAFA;
}

.option-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.option-note {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.image-watermark-info {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  border: 2rpx solid #E9ECEF;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16rpx;
  display: block;
}

.info-content {
  margin-top: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: #6C757D;
  margin-bottom: 12rpx;
  display: block;
}

.info-link {
  font-size: 24rpx;
  color: #007AFF;
  margin-bottom: 8rpx;
  display: block;
  text-decoration: underline;
}

.action-section {
  padding: 0;  /* 移除左右padding，让内部卡片延伸到边缘 */
}

/* 强制刷新样式 - v2.0 */
.extract-btn {
  width: 60% !important;
  max-width: 400rpx !important;
  height: 96rpx !important;
  background: #ffffff !important;
  color: #E1251B !important;
  border: 3rpx solid #E1251B !important;
  border-radius: 48rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  margin: 0 auto 30rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(225, 37, 27, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-icon {
  font-size: 28rpx;
}

.extract-btn.disabled {
  background: #F5F5F5 !important;
  color: #CCCCCC !important;
  border: 3rpx solid #CCCCCC !important;
  box-shadow: none !important;
  opacity: 0.6 !important;
}

.extract-btn:active {
  transform: scale(0.98) !important;
  background: #E1251B !important;
  color: #ffffff !important;
  box-shadow: 0 4rpx 12rpx rgba(225, 37, 27, 0.4) !important;
}

.share-btn-home {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: #ffffff;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.share-btn-home:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 10rpx rgba(255, 107, 107, 0.2);
}

.share-btn-icon {
  font-size: 28rpx;
}

.share-btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

.share-btn-hint {
  font-size: 22rpx;
  opacity: 0.9;
  margin-left: 8rpx;
}

.tips {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-icon-text {
  font-size: 28rpx;
  color: #E1251B;
}

.tutorial-info {
  font-size: 24rpx;
  color: #333;
  margin-top: 16rpx;
}



.tips {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8rpx;
}

/* 常驻广告位样式 */
.ad-placeholder {
  background: #FFFFFF;
  margin: 30rpx 0 20rpx;  /* 左右无空白，上下有间距 */
  border-radius: 16rpx;  /* 圆角设计 */
  padding: 30rpx;
  border: 2rpx dashed #FF6B35;
  min-height: 120rpx;
  width: 100%;  /* 确保宽度一致 */
  box-sizing: border-box;
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.ad-label {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: 600;
}

.ad-tip {
  font-size: 20rpx;
  color: #999;
}

.ad-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  min-height: 80rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.ad-content:active {
  background: #E9ECEF;
  transform: scale(0.98);
}

.ad-text {
  font-size: 24rpx;
  color: #666;
}

.ad-play-icon {
  font-size: 20rpx;
  color: #FF6B35;
}

/* 中间广告位的额外样式 */
.ad-thumbnail {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ad-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.ad-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.ad-source {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.ad-source-icon {
  font-size: 20rpx;
  color: #999;
}

.ad-source-text {
  font-size: 20rpx;
  color: #999;
}

.ad-action-btn {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  flex-shrink: 0;
}

/* 移除处理结果相关的CSS样式 */

.feature-info {
  background: #FFF1F0;
  border: 1rpx solid #FFCCC7;
  border-radius: 16rpx;
  padding: 30rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #E1251B;
  display: block;
  margin-bottom: 20rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
}

.feature-item {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 使用教程链接样式 */
.tutorial-link {
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  border-radius: 20rpx;
  margin: 20rpx 0;
  box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.25);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tutorial-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.tutorial-link:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.3);
}

.tutorial-content {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

.tutorial-content .tutorial-icon {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.tutorial-content .tutorial-icon-text {
  font-size: 28rpx;
  position: relative;
  z-index: 2;
}

.tutorial-content .tutorial-icon-shine {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  z-index: 1;
}

/* 确保上面的样式优先级更高，覆盖其他定义 */
.tutorial-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.tutorial-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 1.2;
}

.tutorial-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}



.tutorial-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  flex-shrink: 0;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

.tutorial-arrow .arrow-text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
  line-height: 1;
}

.disclaimer {
  background: #FFF2F2;
  border: 1rpx solid #FFB3B3;
  border-radius: 16rpx;
  padding: 30rpx;
}

.disclaimer-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #D32F2F;
  display: block;
  margin-bottom: 15rpx;
}

.disclaimer-text {
  font-size: 24rpx;
  color: #D32F2F;
  line-height: 1.6;
}

.processed-content {
  margin-top: 20rpx;
}

.processed-video {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  background: #F5F5F5;
}

.video-status {
  margin-top: 20rpx;
  text-align: center;
}

.status-text {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background: #F0F0F0;
  border-radius: 20rpx;
}

.processed-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 12rpx;
}

.result-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  padding: 0 20rpx;
}

/* 顶部广告位样式 */
.top-ad-placeholder {
  background: #FFFFFF;
  margin: 20rpx 0;  /* 左右无空白，上下有间距 */
  border-radius: 16rpx;  /* 圆角设计 */
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #E0E0E0;
  width: 100%;  /* 确保宽度一致 */
  box-sizing: border-box;
}

/* 广告位和功能卡片统一样式 */
.top-ad-placeholder,
.ad-placeholder,
.feature-info,
.disclaimer {
  margin: 20rpx 0;  /* 上下有间距，左右无空白 */
  padding-left: 24rpx;
  padding-right: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.ad-label {
  font-size: 20rpx;
  color: #999;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.ad-close {
  font-size: 24rpx;
  color: #999;
  cursor: pointer;
  padding: 4rpx;
}

.ad-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.ad-thumbnail {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ad-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.ad-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.ad-source {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.ad-source-icon {
  font-size: 20rpx;
  color: #999;
}

.ad-source-text {
  font-size: 20rpx;
  color: #999;
}

.ad-action-btn {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  flex-shrink: 0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.save-btn {
  background: #34C759;
  color: #ffffff;
}

.share-btn {
  background: #007AFF;
  color: #ffffff;
}

.debug-info {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 8rpx;
  font-family: monospace;
}

/* 倒计时弹窗样式 */
.countdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.countdown-dialog {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 0 40rpx;
  max-width: 600rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.countdown-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.countdown-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.countdown-button {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #ffffff;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: inline-block;
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(225, 37, 27, 0.3);
}

.countdown-button:active {
  opacity: 0.8;
}

/* 添加到我的小程序指示 */
.add-tip-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
}

/* 指向三个点的文字和箭头 */
.add-tip-pointer {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx; /* 减小间距，让箭头和文字框更贴近 */
  pointer-events: auto;
  animation: pointerFloat 2s ease-in-out infinite;
}

.pointer-content {
  background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
  border-radius: 20rpx;
  padding: 18rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(44, 62, 80, 0.3);
  border: 2rpx solid #3498DB;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  min-width: 180rpx;
}

.pointer-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 1.3;
}

.pointer-subtext {
  font-size: 24rpx;
  color: #ECF0F1;
  line-height: 1.3;
  text-align: center;
}

.pointer-arrow {
  font-size: 50rpx;
  color: #3498DB;
  font-weight: bold;
  animation: arrowBounce 1s ease-in-out infinite;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.6);
  margin-bottom: 8rpx;
  order: -1;
}



/* 浮动动画 */
@keyframes pointerFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 箭头弹跳动画 */
@keyframes arrowBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 自定义解析失败弹窗样式 */
.parse-fail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2rpx);
}

.parse-fail-modal {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 40rpx;
  max-width: 640rpx;
  width: 100%;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(1);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  0% {
    transform: scale(0.9) translateY(20rpx);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.parse-fail-header {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  padding: 32rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.parse-fail-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.parse-fail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
}

.parse-fail-close {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 8rpx;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.parse-fail-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.parse-fail-content {
  padding: 40rpx;
}

.parse-fail-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

.parse-fail-reasons {
  margin-bottom: 0;
}

.parse-fail-reason {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff5252;
}

.reason-number {
  background: linear-gradient(135deg, #ff5252 0%, #ff6b6b 100%);
  color: #ffffff;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.reason-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  flex: 1;
}

.parse-fail-footer {
  padding: 0 40rpx 20rpx;
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.parse-fail-btn {
  display: inline-block;
  padding: 14rpx 40rpx;
  border: none;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.cancel-btn {
  background: transparent;
  color: #999999;
  border: none; /* 无边框 */
  box-shadow: none;
}

.cancel-btn:active {
  background: #f5f5f5; /* 点击时有轻微的背景变化 */
}

.retry-btn {
  background: #ffffff;
  color: #E1251B;
  border: 3rpx solid #E1251B;
  box-shadow: 0 8rpx 24rpx rgba(225, 37, 27, 0.3);
  font-weight: 600;
}


.retry-btn:active {
  transform: scale(0.98);
  background: #E1251B;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(225, 37, 27, 0.4);
}



</style>

<style scoped>

</style>
