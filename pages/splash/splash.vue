<template>
  <view class="splash-container">
    <!-- 模拟全屏广告区域 -->
    <view class="ad-container" @click="handleAdClick">
      <!-- 模拟广告内容 -->
      <view class="ad-content">
        <view class="ad-mock-content">
          <text class="ad-title">🎯 模拟广告位</text>
          <text class="ad-subtitle">真实广告时由微信官方提供内容</text>
          <text class="ad-desc">关闭按钮也由微信官方提供</text>
          <text class="ad-note">当前仅为开发测试</text>
        </view>
      </view>
      
      <!-- 简化的顶部控制栏 -->
      <view class="top-control-bar" @click.stop="">
        <!-- 左侧：跳过按钮 -->
        <view class="left-controls">
          <view class="skip-button" @click.stop="skipAd">
            <text class="skip-text">跳过 {{ countdown }}s</text>
          </view>
        </view>

        <!-- 右侧：关闭按钮 -->
        <view class="right-controls">
          <view class="close-button" @click.stop="skipAd">
            <text class="close-icon">×</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部小程序信息 -->
    <view class="app-info">
      <image src="/static/logo.png" class="app-logo" mode="aspectFit"></image>
      <text class="app-name">墨影去水印</text>
      <view class="loading-bar">
        <view class="loading-progress" :style="{ width: loadingProgress + '%' }"></view>
      </view>
    </view>
  </view>
</template>

<script>
import adManager from '../../components/ad-config.js'

export default {
  data() {
    return {
      countdown: 5, // 倒计时秒数
      loadingProgress: 0, // 加载进度
      timer: null,
      progressTimer: null
    }
  },
  
  onLoad() {
    console.log('[启动页] 页面加载')

    // 检查是否应该显示启动页广告
    if (!adManager.shouldShowSplashAd()) {
      console.log('[启动页] 不需要显示广告，直接跳转')
      // 直接跳转到首页
      setTimeout(() => {
        this.skipAd()
      }, 500) // 短暂延迟，避免太突兀
      return
    }

    console.log('[启动页] 显示启动页广告')
    this.startCountdown()
    this.startLoading()
  },
  
  onUnload() {
    this.clearTimers()
  },
  
  methods: {
    // 开始倒计时
    startCountdown() {
      console.log('开始倒计时，初始值:', this.countdown)
      this.timer = setInterval(() => {
        this.countdown--
        console.log('倒计时:', this.countdown)
        if (this.countdown <= 0) {
          console.log('倒计时结束，自动跳转')
          this.skipAd()
        }
      }, 1000)
    },
    
    // 开始加载动画
    startLoading() {
      this.progressTimer = setInterval(() => {
        this.loadingProgress += 2
        if (this.loadingProgress >= 100) {
          this.loadingProgress = 100
          clearInterval(this.progressTimer)
        }
      }, 100)
    },
    
    // 跳过广告
    skipAd() {
      console.log('跳过广告被点击')
      this.clearTimers()

      // 记录启动页广告显示时间
      try {
        uni.setStorageSync('lastSplashAdShowTime', Date.now())
      } catch (e) {
        console.error('存储失败:', e)
      }

      // 检查是否需要显示第一次使用流程
      const isFirstUse = !uni.getStorageSync('hasUsedApp')
      const hasShownAddGuide = uni.getStorageSync('hasShownAddGuide')

      let url = '/pages/watermark-remover/index'
      if (isFirstUse && !hasShownAddGuide) {
        url += '?showFirstUse=true'
        console.log('[启动页] 第一次使用，跳转时携带参数')
      }

      // 跳转到首页
      uni.reLaunch({
        url: url,
        success: () => {
          console.log('跳转成功')
        },
        fail: (err) => {
          console.error('跳转失败:', err)
        }
      })
    },
    
    // 点击广告内容区域（模拟）
    handleAdClick() {
      console.log('[模拟] 广告内容被点击')
      // 真实广告时，这里会由微信处理跳转到广告主页面
      wx.showModal({
        title: '模拟广告点击',
        content: '这是模拟广告点击效果\n真实广告会跳转到广告主页面',
        showCancel: false,
        confirmText: '知道了'
      })
    },
    

    
    // 清理定时器
    clearTimers() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    }
  }
}
</script>

<style scoped>
.splash-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 广告容器 */
.ad-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 模拟广告内容 */
.ad-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-mock-content {
  text-align: center;
  color: #FFFFFF;
}

.ad-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 30rpx;
}

.ad-subtitle {
  font-size: 32rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.9;
}

.ad-desc {
  font-size: 28rpx;
  display: block;
  opacity: 0.7;
  margin-bottom: 15rpx;
}

.ad-note {
  font-size: 24rpx;
  display: block;
  opacity: 0.6;
  font-style: italic;
}

/* 顶部控制栏 - 按照参考图片设计 */
.top-control-bar {
  position: absolute;
  top: 60rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  z-index: 10;
}

/* 简化的控制区域 */
.left-controls {
  display: flex;
  align-items: center;
}

.skip-button {
  background: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  cursor: pointer;
}

.skip-button:active {
  background: rgba(0, 0, 0, 0.8);
}

.right-controls {
  display: flex;
  align-items: center;
}

.close-button {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-button:active {
  background: rgba(255, 255, 255, 0.7);
}

.close-icon {
  color: #333333;
  font-size: 32rpx;
  font-weight: bold;
}

/* 底部小程序信息 */
.app-info {
  background: rgba(0, 0, 0, 0.8);
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200rpx;
}

.app-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.app-name {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
}

/* 加载进度条 */
.loading-bar {
  width: 200rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 3rpx;
  transition: width 0.1s ease;
}

/* 安全区域适配 */
.top-control-bar {
  top: calc(60rpx + env(safe-area-inset-top));
}
</style>
