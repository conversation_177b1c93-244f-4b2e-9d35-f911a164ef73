<script>
  import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update';

  export default {
    globalData: {
      lastHideTime: null,      // 存储切出时的时间戳
      isBackground: false,     // 标记是否在后台
      isFirstTimeUser: false   // 标记当前是否是第一次使用（在 onLaunch 中设置）
    },

    onLaunch: async function(options) {
      console.log('🚀 [App] 应用启动', 'scene:', options.scene)

      // 移除安全网络初始化，避免调用uni-id-co
      // #ifdef MP-WEIXIN
      // uniCloud.initSecureNetworkByWeixin() // 已禁用，避免uni-id错误
      // #endif
      
      checkUpdate() //更新升级

      // 🔒 检查隐私保护指引
      this.checkPrivacySetting()

      // 🎯 检查是否为第一次使用
      this.checkFirstTimeUse()
    },

    onShow: function(options) {
      console.log('📱 [App] 应用显示', 'scene:', options.scene)
      
      // 🎯 检查重新进入或应用切换
      this.checkReenterOrAppSwitch(options)
    },

    onHide: function() {
      console.log('🙈 [App] 应用隐藏')
      // 记录切出时间和后台状态
      this.globalData.lastHideTime = Date.now()
      this.globalData.isBackground = true
    },

    methods: {
      // 🔒 检查隐私保护指引
      checkPrivacySetting() {
        // #ifdef MP-WEIXIN
        console.log('🔒 [App] 检查隐私保护指引')
        try {
          wx.getPrivacySetting({
            success: res => {
              console.log('🔒 [App] 隐私检查结果:', res)
              console.log('🔒 [App] 详细信息:')
              console.log('   - needAuthorization:', res.needAuthorization)
              console.log('   - privacyContractName:', res.privacyContractName)
              console.log('   - errMsg:', res.errMsg)
              
              if (res.needAuthorization) {
                console.log('🔒 [App] ✅ 需要弹出隐私授权弹窗')
                // 微信会自动弹出官方隐私保护指引弹窗
              } else {
                console.log('🔒 [App] ❌ 不需要弹出隐私授权弹窗')
                
                // 检查可能的原因
                if (res.privacyContractName) {
                  console.log('🔒 [App] 🤔 隐私协议名称存在，但不需要授权')
                  console.log('🔒 [App] 可能原因：')
                  console.log('   1. 用户之前已经授权过')
                  console.log('   2. 配置还在同步中（审核通过后需要1-24小时生效）')
                  console.log('   3. 客户端缓存未更新')
                } else {
                  console.log('🔒 [App] ⚠️ 未检测到隐私协议名称')
                }
              }
            },
            fail: err => {
              console.error('🔒 [App] 隐私检查失败:', err)
            }
          })
        } catch (error) {
          console.error('🔒 [App] 隐私检查异常:', error)
        }
        // #endif
      },

      // 🎯 检查第一次使用
      checkFirstTimeUse() {
        const hasUsedApp = uni.getStorageSync('hasUsedApp')
        console.log('🆕 [App] 检查第一次使用，当前存储值 hasUsedApp:', hasUsedApp)

        // 若未存储过标记，判定为"第一次使用"
        if (!hasUsedApp) {
          console.log('🆕 [App] 触发【第一次使用】事件')

          // 设置全局标记，表示当前是第一次使用
          this.globalData.isFirstTimeUser = true

          // 延迟触发，确保页面加载完成
          setTimeout(() => {
            console.log('🆕 [App] 延迟执行第一次使用逻辑')
            this.handleFirstTimeUse()
          }, 1000)

          // 存储"已使用"标记
          uni.setStorageSync('hasUsedApp', true)
          console.log('🆕 [App] 已设置 hasUsedApp 为 true')
        } else {
          console.log('🆕 [App] 不是第一次使用，跳过')
        }
      },

      // 🎯 检查重新进入或应用切换
      checkReenterOrAppSwitch(options) {
        // 1. 排除"第一次使用"场景
        if (this.globalData.isFirstTimeUser) {
          console.log('🔄 [App] 跳过第一次使用场景，不触发重新进入或应用切换事件')
          return
        }

        // 2. 检查是否为应用切换（从后台切回前台）
        if (this.isAppSwitchFromBackground()) {
          console.log('🔀 [App] 触发【应用切换】事件')
          setTimeout(() => {
            this.handleAppSwitch()
          }, 1000) // 增加延迟，确保页面加载完成
          return
        }

        // 3. 检查是否为重新进入场景
        if (this.isReenterScene(options.scene)) {
          console.log('🔄 [App] 触发【重新进入程序】事件', '场景:', this.getSceneDesc(options.scene))
          setTimeout(() => {
            this.handleReenterApp(options.scene)
          }, 1500) // 增加延迟，确保页面加载完成
          return
        }
      },

      // 🔍 判断是否为重新进入场景
      isReenterScene(scene) {
        // 常见的重新进入场景值
        const reEnterScenes = [
          1001, // 最近使用栏进入
          1011, // 搜索进入  
          1012, // 扫码进入
          1089, // 我的小程序列表进入
          1047, // 扫描小程序码
          1048, // 长按图片识别小程序码
          1049  // 手机相册选取小程序码
        ]
        return reEnterScenes.includes(scene)
      },

      // 🔍 判断是否为应用切换（从后台切回前台）
      isAppSwitchFromBackground() {
        const currentTime = Date.now()
        const hideTime = this.globalData.lastHideTime
        const maxInterval = 30 * 60 * 1000 // 30分钟

        return hideTime && 
               currentTime - hideTime < maxInterval && 
               this.globalData.isBackground
      },

      // 🎯 第一次使用事件处理
      handleFirstTimeUse() {
        console.log('🆕 [App] 执行第一次使用逻辑')

        // 发送事件到页面
        const eventData = {
          type: 'firstTimeUse',
          timestamp: Date.now()
        }
        console.log('🆕 [App] 发送第一次使用事件:', eventData)
        uni.$emit('firstTimeUseEvent', eventData)

        // 延迟重置第一次使用标记，允许后续的应用切换事件
        setTimeout(() => {
          this.globalData.isFirstTimeUser = false
          console.log('🆕 [App] 重置第一次使用标记，允许应用切换事件')
        }, 5000) // 5秒后重置，给用户足够时间看完弹窗
      },

      // 🎯 重新进入小程序事件处理
      handleReenterApp(scene) {
        console.log('🔄 [App] 执行重新进入小程序逻辑')

        // 发送事件到页面
        const eventData = {
          type: 'reenterApp',
          scene: scene,
          sceneDesc: this.getSceneDesc(scene),
          timestamp: Date.now()
        }
        console.log('🔄 [App] 发送重新进入事件:', eventData)
        uni.$emit('reenterAppEvent', eventData)
      },

      // 🎯 应用切换事件处理
      handleAppSwitch() {
        const currentTime = Date.now()
        const hideTime = this.globalData.lastHideTime
        const switchDuration = Math.floor((currentTime - hideTime) / 1000)
        
        console.log('🔀 [App] 执行应用切换逻辑，切出时长:', switchDuration + '秒')
        
        // 发送事件到页面
        uni.$emit('appSwitchEvent', {
          type: 'appSwitch',
          duration: switchDuration,
          hideTime: hideTime,
          showTime: currentTime,
          timestamp: Date.now()
        })

        // 重置后台标记
        this.globalData.isBackground = false
        this.globalData.lastHideTime = null
      },

      // 🔍 将场景值转为文字描述
      getSceneDesc(scene) {
        const descMap = {
          1001: '最近使用栏进入',
          1011: '搜索进入',
          1012: '扫码进入',
          1089: '我的小程序列表进入',
          1047: '扫描小程序码',
          1048: '长按图片识别小程序码',
          1049: '手机相册选取小程序码'
        }
        return descMap[scene] || `其他方式（场景值：${scene}）`
      }
    }
  }
</script>

<style>
  /*每个页面公共css */
  /* #ifndef APP-NVUE */
  view {
    box-sizing: border-box;
  }

  @font-face {
    font-family: "iconfont";
    src: url('https://at.alicdn.com/t/font_2354462_s00xh8caffp.ttf');
  }

  .ico {
    font-family: iconfont;
  }

  /* #endif */
</style>
