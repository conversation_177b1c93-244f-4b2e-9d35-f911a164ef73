// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": ["appid", "name", "description"],
	"permission": {
		"read": false,
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"appid": {
			"bsonType": "string",
			"description": "应用的AppID",
			"label": "AppID",
			"componentForEdit": {
				"name": "uni-easyinput",
				"props": {
					"disabled": true
				}
			}
		},
		"name": {
			"bsonType": "string",
			"description": "应用名称",
			"label": "应用名称",
			"componentForEdit": {
				"name": "uni-easyinput",
				"props": {
					"disabled": true
				}
			}
		},
		"description": {
			"bsonType": "string",
			"description": "应用描述",
			"label": "应用描述",
			"componentForEdit": {
				"name": "textarea"
			},
			"componentForShow": {
				"name": "textarea",
				"props": {
					"disabled": true
				}
			}
		},
		"create_date": {
			"bsonType": "timestamp",
			"label": "创建时间",
			"forceDefaultValue": {
				"$env": "now"
			},
			"componentForEdit": {
				"name": "uni-dateformat"
			}
		}
	}
}
