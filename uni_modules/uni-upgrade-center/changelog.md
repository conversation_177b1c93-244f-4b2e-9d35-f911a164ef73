## 0.5.1（2022-07-06）
- 修复 上版带出云函数不存在的Bug
- 升级 uni-admin 大于等于 1.9.0 务必更新至此版本。uni-admin 版本小于 1.9.0 请不要更新，历史版本在 Gitee 有发行版。后续 uni-admin 会集成升级中心
## 0.5.0（2022-07-05）
- 修复 版本列表默认显示全部版本的Bug
- 升级 uni-admin 1.9.0 务必更新至此版本。uni-admin 版本小于 1.9.0 请不要更新，后续 uni-admin 会集成升级中心

## 0.4.2（2021-12-07）
- 更新 优化 list 页面显示，修复 list 页面报错
## 0.4.1（2021-12-01）
- 修复 0.4.0版本带出来，发布新版时 appid、name 不会自动填充的Bug
## 0.4.0（2021-11-26）
- 更新 升级中心移除应用管理，现在由uni-admin接管。旧版本若没有应用管理请做升级处理
## 0.3.0（2021-11-18）
- 兼容 uni-admin 新版内置 $request 函数改动
## 0.2.2（2021-09-06）
- 解决 opendb-app-list表对应的schema名称冲突的问题
## 0.2.1（2021-09-03）
- 修复 一个在添加菜单时报错，createdate不与默认值匹配 的Bug
## 0.2.0（2021-08-25）
- 兼容vue3.0
## 0.1.9（2021-08-13）
- 更新  uni-forms使用validate校验字段
- 修复  报错dirty_data、create_date在数据库中并不存在
## 0.1.8（2021-08-09）
- 修复  默认配置项配置错误
## 0.1.7（2021-08-09）
- 移除测试时配置项
## 0.1.6（2021-08-09）
- 修复  修改版本信息时，上传时间丢失问题
## 0.1.5（2021-07-21）
- 更新  :value.sync 改为 :value 和 @update:value
## 0.1.4（2021-07-13）
- 修复  uni-easyinput去除输入字符长度限制
- 更新文档  关于 uni-id缺少配置信息 错误。请查看安装指引第13条
## 0.1.3（2021-06-15）
- 修复 wgt更新某些情况下获取数据错误
## 0.1.2（2021-06-04）
- 修复 上传包时根据平台筛选文件
- 更新 文档
## 0.1.1（2021-05-18）
- 更新uni-table中uni-tr组件的selectable属性为disabled
## 0.1.0（2021-04-07）
- 更新版本对比函数 compare
## 0.0.6（2021-04-01）
- 调整db_init.json
## 0.0.5（2021-03-25）
- 调整为uni_modules目录
- 升级中心后台管理系统拆分为 Admin 后台管理 和 前台检查更新（uni-upgrade-center-app）
