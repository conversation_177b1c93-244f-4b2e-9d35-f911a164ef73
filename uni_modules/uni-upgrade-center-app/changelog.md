## 0.6.1（2022-08-17）
- 修复 后台添加应用市场，但都没有启用的情况下报错的Bug （需要 uni-admin 1.9.3+）
## 0.6.0（2022-07-19）
- 新增 支持多应用商店配置（需要 uni-admin 1.9.3+）
## 0.4.1（2022-05-27）
- 修复 上版引出的报错问题
## 0.4.0（2022-05-27）
- 新增 Android 支持跳转手机自带商店，填写升级包地址时请填写跳转商店链接
- 新增 改为云对象调用方式，使用更直观
## 0.3.3（2022-04-14）
- 修复  调用 check-update，当 code 为 0 时没有回调
## 0.3.2（2022-01-12）
- 优化显示逻辑
## 0.3.1（2021-11-24）
- 修复 vue3 上图片不显示的Bug
## 0.3.0（2021-11-18）
- 移除 wgt 安装成功后提示，防止重启过快弹框不消失
## 0.2.2（2021-08-25）
- 兼容vue3.0
## 0.2.1（2021-07-26）
- 修复  使用腾讯云并手动填写地址时，导致下载链接失效的bug
## 0.2.0（2021-07-13）
- 更新文档  关于报错local_storage_key 为空，请不要将页面路径设置为pages.json中第一项
## 0.1.9（2021-06-28）
- 更新文档
- 修复  wgt安装失败时，按钮状态不对
## 0.1.8（2021-06-16）
- 修复  跳转安装时，导致上次下载的apk还没安装就被删掉的bug
## 0.1.7（2021-06-03）
- 修改  移除static中的图片
## 0.1.6（2021-06-03）
- 修改  下载更新按钮使用CSS渐变色
## 0.1.5（2021-04-22）
- 更新check-update函数。现在返回一个Promise，有更新时成功回调，其他情况错误回调
## 0.1.4（2021-04-13）
- 更新文档。明确云函数调用结果
## 0.1.3（2021-04-13）
- 解耦云函数与弹框处理。utils中新增 call-check-version.js，可用于单独检测是否有更新
## 0.1.2（2021-04-07）
- 更新版本对比函数 compare
## 0.1.1（2021-04-07）
- 修复 腾讯云空间下载链接不能下载问题
## 0.1.0（2021-04-07）
- 新增使用uni.showModal提示升级示例
- 修改iOS升级提示方式
## 0.0.7（2021-04-02）
- 修复在iOS上打开弹框报错
## 0.0.6（2021-04-01）
- 兼容旧版本安卓
## 0.0.5（2021-04-01）
- 修复低版本安卓上进度条错位
## 0.0.4（2021-04-01）
- 更新readme
- 修复check-update语法错误
## 0.0.3（2021-04-01）
- 新增前台更新弹框，详见readme
- 更新前台检查更新方法

## 0.0.2（2021-03-29）
- 更新文档
- 移除 dependencies

## 0.0.1（2021-03-25）
- 升级中心前台检查更新
