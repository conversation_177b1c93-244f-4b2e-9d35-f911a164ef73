/**
 * 添加到我的小程序提示配置
 * 统一管理提示相关的配置参数
 */

const addTipConfig = {
  // 全局开关
  globalEnabled: true,

  // 功能开关 - 细粒度控制
  features: {
    // 首次使用时的添加常用小程序确认框
    firstUseDialog: {
      enabled: true,                    // ✅ 启用首次使用确认框
      title: '下次可以这样找到我',
      description: '首次使用时显示的添加引导确认框'
    },

    // 重新进入时的悬浮提示
    floatingTip: {
      enabled: true,                    // ✅ 启用悬浮提示
      duration: 10000,                  // 显示20秒
      description: '重新进入小程序时显示的悬浮提示'
    }
  },

  // 显示配置
  display: {
    // 延迟显示时间（毫秒）
    showDelay: 2000,

    // 自动隐藏时间（毫秒），设为0表示不自动隐藏
    autoHideDelay: 10000,

    // 是否只显示一次（改为false，每次都显示）
    showOnlyOnce: false,

    // 存储键名
    storageKey: 'hasShownAddTip'
  },
  
  // 位置配置
  position: {
    // 箭头和文字位置（在标题上方，箭头朝上指向三个点）
    pointer: {
      top: '20rpx',
      right: '20rpx'
    }
  },
  
  // 内容配置
  content: {
    mainText: '点击这里',
    subText: '添加到我的小程序',
    arrow: '⬆️'
  },

  // 样式配置
  style: {
    // 箭头样式
    arrow: {
      fontSize: '40rpx',
      color: '#FF6B35',
      marginBottom: '10rpx',
      textAlign: 'center'
    },
    // 主文字样式
    mainText: {
      fontSize: '28rpx',
      color: '#FF6B35',
      fontWeight: 'bold',
      marginBottom: '5rpx',
      textAlign: 'center'
    },
    // 副文字样式
    subText: {
      fontSize: '22rpx',
      color: '#666',
      lineHeight: '1.2',
      textAlign: 'center'
    },
    // 容器样式
    container: {
      backgroundColor: '#FFF',
      borderRadius: '16rpx',
      padding: '20rpx',
      boxShadow: '0 4rpx 20rpx rgba(0,0,0,0.1)',
      border: '2rpx solid #FF6B35',
      maxWidth: '300rpx',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }
  },

  // 动画配置
  animation: {
    // 浮动动画持续时间（秒）
    floatDuration: 2,

    // 箭头弹跳动画持续时间（秒）
    bounceDuration: 1
  }
}

/**
 * 获取配置
 */
function getConfig() {
  return addTipConfig
}

/**
 * 检查是否应该显示提示（通用方法，保持向后兼容）
 */
function shouldShowTip() {
  if (!addTipConfig.globalEnabled) {
    return false
  }

  if (addTipConfig.display.showOnlyOnce) {
    const hasShown = uni.getStorageSync(addTipConfig.display.storageKey)
    return !hasShown
  }

  return true
}

/**
 * 检查是否应该显示首次使用确认框
 */
function shouldShowFirstUseDialog() {
  // 检查全局开关
  if (!addTipConfig.globalEnabled) {
    console.log('[添加提示] globalEnabled = false，跳过首次使用确认框')
    return false
  }

  // 检查功能开关
  if (!addTipConfig.features.firstUseDialog.enabled) {
    console.log('[添加提示] firstUseDialog.enabled = false，跳过首次使用确认框')
    return false
  }

  return true
}

/**
 * 检查是否应该显示悬浮提示
 */
function shouldShowFloatingTip() {
  // 检查全局开关
  if (!addTipConfig.globalEnabled) {
    console.log('[添加提示] globalEnabled = false，跳过悬浮提示')
    return false
  }

  // 检查功能开关
  if (!addTipConfig.features.floatingTip.enabled) {
    console.log('[添加提示] floatingTip.enabled = false，跳过悬浮提示')
    return false
  }

  return true
}

/**
 * 标记已显示
 */
function markAsShown() {
  if (addTipConfig.display.showOnlyOnce) {
    uni.setStorageSync(addTipConfig.display.storageKey, true)
  }
}





export default {
  getConfig,
  shouldShowTip,
  shouldShowFirstUseDialog,
  shouldShowFloatingTip,
  markAsShown
}
