<template>
  <view class="text-content">
    <view class="text-content-container">
      <view class="text-icon">📝</view>
      <text class="text-title">文本内容</text>
      <view class="text-content-display">
        <text class="text-content-text">{{ content }}</text>
      </view>
      <view class="text-info">
        <text class="text-info-item">字数：{{ textLength }}</text>
        <text v-if="shouldShowSource" class="text-info-item">来源：{{ displaySource }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import adManager from '../ad-config.js'

export default {
  name: 'ResultTextContent',
  props: {
    content: {
      type: String,
      default: ''
    },
    source: {
      type: String,
      default: ''
    },
    platform: {
      type: String,
      default: ''
    }
  },
  computed: {
    textLength() {
      return this.content ? this.content.length : 0
    },
    shouldShowSource() {
      return adManager.config.showPlatformSource
    },
    displaySource() {
      if (!this.shouldShowSource) {
        return '短视频平台'
      }
      return adManager.getPlatformDisplayName(this.platform) || this.source || '短视频平台'
    }
  }
}
</script>

<style scoped>
.text-content {
  width: 100%;
}

.text-content-container {
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 50%, #E1251B 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  color: #ffffff;
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.text-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.9;
}

.text-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  opacity: 0.9;
}

.text-content-display {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  width: 100%;
  min-height: 200rpx;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.text-content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #ffffff;
  word-break: break-all;
  white-space: pre-wrap;
  display: block;
  text-align: left;
}

.text-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
  opacity: 0.8;
}

.text-info-item {
  font-size: 24rpx;
  color: #ffffff;
}
</style>