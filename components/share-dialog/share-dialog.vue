<template>
  <view v-if="showDialog" class="share-overlay" @tap="hideDialog">
    <view class="share-dialog" @tap.stop>
      <!-- 标题栏 -->
      <view class="share-header">
        <view class="share-title">分享小程序</view>
        <view class="share-close" @tap="hideDialog">✕</view>
      </view>
      
      <!-- 奖励提示 -->
      <view v-if="showRewardHint && canGetReward" class="reward-hint">
        <view class="reward-icon">🎁</view>
        <view class="reward-text">分享成功后可获得24小时无限解析权限</view>
      </view>
      
      <!-- 分享方式列表 -->
      <view class="share-options">
        <!-- 微信好友 -->
        <view 
          v-if="shareConfig.wechatFriend?.enabled" 
          class="share-option" 
          @tap="shareToWechat"
        >
          <view class="share-icon wechat-friend">💬</view>
          <view class="share-label">微信好友</view>
        </view>
        
        <!-- 朋友圈 -->
        <view 
          v-if="shareConfig.wechatMoments?.enabled" 
          class="share-option" 
          @tap="shareToMoments"
        >
          <view class="share-icon wechat-moments">🌟</view>
          <view class="share-label">朋友圈</view>
        </view>
        
        <!-- 复制链接 -->
        <view 
          v-if="shareConfig.copyLink?.enabled" 
          class="share-option" 
          @tap="copyShareLink"
        >
          <view class="share-icon copy-link">🔗</view>
          <view class="share-label">复制链接</view>
        </view>
      </view>
      
      <!-- 取消按钮 -->
      <view class="share-cancel" @tap="hideDialog">
        取消
      </view>
    </view>
  </view>
</template>

<script>
import shareManager from '../share-config.js'

export default {
  name: 'ShareDialog',
  data() {
    return {
      showDialog: false,
      shareConfig: {},
      entryType: 'resultPage',
      showRewardHint: true,
      callback: null,
      canGetReward: false,
      isHandling: false // 防止重复处理
    }
  },
  
  mounted() {
    // 监听显示分享弹窗事件
    uni.$on('showShareDialog', this.handleShowShareDialog)

    // 监听页面隐藏事件，确保弹窗关闭
    uni.$on('onHide', this.handlePageHide)

    // 监听页面切换事件，强制关闭弹窗
    uni.$on('onPageSwitch', this.handlePageSwitch)
  },

  beforeDestroy() {
    // 清理事件监听
    uni.$off('showShareDialog', this.handleShowShareDialog)
    uni.$off('onHide', this.handlePageHide)
    uni.$off('onPageSwitch', this.handlePageSwitch)
  },
  
  methods: {
    // 处理显示分享弹窗事件
    handleShowShareDialog(options) {
      console.log('[分享弹窗] 收到显示事件，直接调用微信分享')
      console.log('[分享弹窗] 传入参数:', {
        config: options?.config,
        entryType: options?.entryType,
        showRewardHint: options?.showRewardHint,
        hasCallback: !!options?.callback
      })

      // 🔧 修复：先检查是否正在处理，如果是则强制重置后再继续
      if (this.isHandling) {
        console.warn('[分享弹窗] 检测到上次分享未正常结束，强制重置状态')
        this.isHandling = false
      }

      this.isHandling = true

      this.shareConfig = options.config.shareTypes
      this.entryType = options.entryType
      this.showRewardHint = options.showRewardHint
      this.callback = options.callback

      // 检查是否可以获得权限
      const permissionCheck = shareManager.canGetSharePermission()
      this.canGetReward = permissionCheck.allowed
      
      console.log('[分享弹窗] 初始化完成:', {
        shareConfig: !!this.shareConfig,
        wechatFriendEnabled: this.shareConfig?.wechatFriend?.enabled,
        canGetReward: this.canGetReward,
        permissionCheck: permissionCheck
      })

      // 直接调用微信好友分享，不显示弹窗
      this.shareToWechat()
    },
    
    // 隐藏弹窗
    hideDialog() {
      console.log('[分享弹窗] 隐藏弹窗')
      this.showDialog = false
      this.callback = null
      this.isHandling = false
    },

    // 处理页面隐藏事件
    handlePageHide() {
      console.log('[分享弹窗] 页面隐藏，强制关闭弹窗')
      if (this.showDialog) {
        this.hideDialog()
      }
    },

    // 处理页面切换事件
    handlePageSwitch() {
      console.log('[分享弹窗] 页面切换，强制关闭弹窗')
      if (this.showDialog) {
        this.hideDialog()
      }
    },
    
    // 分享到微信好友（简化版）
    shareToWechat() {
      console.log('[分享弹窗] 开始分享到微信好友')
      
      // 检查是否为测试模式
      const isTestMode = shareManager.config.testMode === true
      console.log('[分享弹窗] 测试模式:', isTestMode)
      
      if (isTestMode) {
        console.log('[分享弹窗] 测试模式，模拟分享成功')
        uni.showToast({
          title: '分享成功（测试模式）',
          icon: 'success',
          duration: 1500
        })
        this.handleShareSuccess('wechatFriend')
        return
      }
      
      const config = this.shareConfig.wechatFriend
      if (!config) {
        console.error('[分享弹窗] 分享配置不存在')
        this.handleShareFail('wechatFriend', { errMsg: '分享配置错误' })
        return
      }

      console.log('[分享弹窗] 调用微信分享API')
      
      // 🔧 添加超时重置机制，防止isHandling永远为true
      const resetTimeout = setTimeout(() => {
        console.warn('[分享弹窗] 分享API调用超时，重置处理状态')
        this.isHandling = false
      }, 10000) // 10秒超时
      
      uni.shareAppMessage({
        title: config.title || '短视频去水印工具',
        desc: config.description || '免费去除视频水印',
        imageUrl: config.imageUrl || '',
        path: '/pages/watermark-remover/index',
        success: (res) => {
          clearTimeout(resetTimeout)
          console.log('[分享弹窗] 分享成功:', res)
          this.handleShareSuccess('wechatFriend')
        },
        fail: (err) => {
          clearTimeout(resetTimeout)
          console.error('[分享弹窗] 分享失败:', err)
          
          // 简化错误处理
          if (err?.errMsg?.includes('cancel') || err?.errMsg?.includes('取消')) {
            console.log('[分享弹窗] 用户取消分享')
            this.handleShareCancel('wechatFriend')
          } else {
            this.handleShareFail('wechatFriend', err)
          }
        },
        complete: (res) => {
          // 🔧 确保无论成功失败都能重置状态
          clearTimeout(resetTimeout)
          console.log('[分享弹窗] 分享操作完成')
          if (this.isHandling) {
            console.log('[分享弹窗] 强制重置处理状态')
            this.isHandling = false
          }
        }
      })
    },
    
    // 分享到朋友圈
    shareToMoments() {
      console.log('[分享弹窗] 分享到朋友圈')

      // 检查是否为测试模式
      const isTestMode = shareManager.config.testMode
      
      if (isTestMode) {
        console.log('[分享弹窗] 测试模式，直接模拟朋友圈分享成功并获得24小时权限')
        uni.showToast({
          title: '分享成功（测试模式）',
          icon: 'success',
          duration: 1500
        })
        this.handleShareSuccess('wechatMoments')
        return
      }

      // 检查分享API是否可用
      if (typeof uni.shareToWeChat !== 'function') {
        console.warn('[分享弹窗] shareToWeChat API 不可用')
        uni.showToast({
          title: '朋友圈分享不可用',
          icon: 'none',
          duration: 2000
        })
        this.handleShareFail('wechatMoments', { errMsg: 'shareToWeChat API 不可用' })
        return
      }

      const config = this.shareConfig.wechatMoments

      uni.shareToWeChat({
        scene: 'WXSceneTimeline', // 朋友圈
        title: config.title,
        summary: config.description,
        imageUrl: config.imageUrl,
        miniProgram: {
          id: '', // 小程序原始ID
          path: '/pages/watermark-remover/index',
          type: 0, // 正式版:0，开发版:1，体验版:2
          webUrl: '' // 兼容低版本的网页链接
        },
        success: (res) => {
          console.log('[分享弹窗] 朋友圈分享成功', res)
          this.handleShareSuccess('wechatMoments')
        },
        fail: (err) => {
          console.log('[分享弹窗] 朋友圈分享失败', err)
          // 真实环境中分享失败就提示失败
          this.handleShareFail('wechatMoments', err)
        }
      })
    },
    
    // 复制分享链接
    copyShareLink() {
      console.log('[分享弹窗] 复制分享链接')
      
      const config = this.shareConfig.copyLink
      
      uni.setClipboardData({
        data: config.linkTemplate,
        success: () => {
          console.log('[分享弹窗] 复制链接成功')
          uni.showToast({
            title: '链接已复制',
            icon: 'success',
            duration: 2000
          })
          this.handleShareSuccess('copyLink')
        },
        fail: (err) => {
          console.log('[分享弹窗] 复制链接失败', err)
          this.handleShareFail('copyLink', err)
        }
      })
    },
    
    // 处理分享成功
    handleShareSuccess(shareType) {
      this.isHandling = false

      // 调用分享管理器处理分享结果
      shareManager.handleShareSuccess({
        success: true,
        shareType: shareType,
        timestamp: Date.now()
      })

      if (this.callback) {
        this.callback({
          success: true,
          shareType: shareType,
          timestamp: Date.now()
        })
      }
    },

    // 处理分享取消
    handleShareCancel(shareType) {
      console.log('[分享弹窗] 用户取消分享', shareType)
      this.isHandling = false

      if (this.callback) {
        this.callback({
          success: false,
          shareType: shareType,
          cancelled: true,
          timestamp: Date.now()
        })
      }
    },
    
    // 处理分享失败（简化版）
    handleShareFail(shareType, error) {
      console.error('[分享弹窗] 分享失败:', shareType)
      console.error('[分享弹窗] 错误信息:', error?.errMsg || '未知错误')
      
      // 简化错误提示
      const errorMessage = error?.errMsg || '分享失败，请重试'
      
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
      
      this.isHandling = false
      
      if (this.callback) {
        this.callback({
          success: false,
          shareType: shareType,
          error: error,
          timestamp: Date.now()
        })
      }
    }
  }
}
</script>

<style scoped>
.share-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 9999;
}

.share-dialog {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.share-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.reward-hint {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.reward-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.reward-text {
  flex: 1;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

.share-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  background-color: #f8f9fa;
  min-width: 160rpx;
}

.share-option:active {
  background-color: #e9ecef;
}

.share-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}

.wechat-friend {
  background-color: #07c160;
  color: #ffffff;
}

.wechat-moments {
  background-color: #1aad19;
  color: #ffffff;
}

.copy-link {
  background-color: #576b95;
  color: #ffffff;
}

.share-label {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.share-cancel {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #666666;
}

.share-cancel:active {
  background-color: #e9ecef;
}
</style>
