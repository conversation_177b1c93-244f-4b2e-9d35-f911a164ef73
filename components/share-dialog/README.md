# ShareDialog 分享弹窗组件

## 📋 组件概述

ShareDialog 是一个完全可插拔的分享弹窗组件，支持多种分享方式，并集成了分享奖励机制。设计理念与广告框架保持一致，实现模块化、可配置、事件驱动的架构。

## 🎯 主要功能

- **多种分享方式**: 支持微信好友、朋友圈、复制链接等
- **分享奖励**: 分享成功后可获得24小时无限解析权限
- **防刷机制**: 防止用户恶意刷分享获取奖励
- **事件驱动**: 基于 uni.$emit/$on 的通信机制
- **完全可配置**: 通过配置文件控制所有功能

## 🏗️ 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <view>
    <!-- 你的页面内容 -->
    
    <!-- 分享弹窗组件 -->
    <ShareDialog />
  </view>
</template>

<script>
import ShareDialog from '@/components/share-dialog/share-dialog.vue'

export default {
  components: {
    ShareDialog
  }
}
</script>
```

### 2. 触发分享弹窗

```javascript
import shareManager from '@/components/share-config.js'

// 显示分享弹窗
shareManager.showShareDialog({
  entryType: 'resultPage',      // 分享入口类型
  showRewardHint: true          // 是否显示奖励提示
})
```

### 3. 监听分享结果

```javascript
// 监听分享奖励事件
uni.$on('shareRewardGranted', (rewardInfo) => {
  console.log('用户获得分享奖励', rewardInfo)
  // 更新UI状态，显示权限状态等
})
```

## ⚙️ 配置选项

### 分享方式配置

```javascript
// 在 share-config.js 中配置
shareTypes: {
  wechatFriend: {
    enabled: true,                      // 启用微信好友分享
    title: 'MarkEraser - 短视频去水印工具',
    description: '免费去除抖音、快手、小红书等平台视频水印',
    imageUrl: '/static/logo.png'
  },
  wechatMoments: {
    enabled: true,                      // 启用朋友圈分享
    title: 'MarkEraser - 短视频去水印工具',
    description: '免费去除抖音、快手、小红书等平台视频水印',
    imageUrl: '/static/logo.png'
  },
  copyLink: {
    enabled: true,                      // 启用复制链接分享
    linkTemplate: '推荐一个好用的短视频去水印工具：MarkEraser，支持抖音、快手、小红书等多个平台！'
  }
}
```

### 奖励机制配置

```javascript
shareReward: {
  enabled: true,                        // 启用分享奖励
  rewardDuration: 24 * 60 * 60 * 1000, // 奖励时长：24小时
  cooldownPeriod: 24 * 60 * 60 * 1000, // 冷却时间：24小时
  maxRewardsPerDay: 1                   // 每日最大奖励次数
}
```

## 🎨 样式定制

组件使用 scoped 样式，可以通过以下方式定制：

```css
/* 修改弹窗背景色 */
.share-dialog {
  background-color: #ffffff;
}

/* 修改奖励提示样式 */
.reward-hint {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
}

/* 修改分享选项样式 */
.share-option {
  background-color: #f8f9fa;
}
```

## 🔧 API 接口

### shareManager.showShareDialog(options)

显示分享弹窗

**参数:**
- `options.entryType` (string): 分享入口类型，如 'resultPage', 'profilePage'
- `options.showRewardHint` (boolean): 是否显示奖励提示，默认 true

**示例:**
```javascript
shareManager.showShareDialog({
  entryType: 'resultPage',
  showRewardHint: true
})
```

### shareManager.canGetShareReward()

检查是否可以获得分享奖励

**返回值:**
```javascript
{
  allowed: boolean,     // 是否允许获得奖励
  reason: string        // 原因说明
}
```

### shareManager.getShareStats()

获取分享统计信息

**返回值:**
```javascript
{
  lastShareTime: number,      // 上次分享时间
  lastRewardTime: number,     // 上次获得奖励时间
  dailyShareCount: number,    // 今日分享次数
  dailyRewardCount: number,   // 今日奖励次数
  totalShares: number,        // 总分享次数
  totalRewards: number,       // 总奖励次数
  canGetReward: boolean,      // 是否可以获得奖励
  nextRewardTime: number      // 下次可获得奖励时间
}
```

## 🎪 事件系统

### 监听事件

```javascript
// 分享奖励发放事件
uni.$on('shareRewardGranted', (rewardInfo) => {
  console.log('分享奖励已发放', rewardInfo)
})
```

### 触发事件

```javascript
// 显示分享弹窗事件
uni.$emit('showShareDialog', {
  config: shareConfig,
  entryType: 'resultPage',
  showRewardHint: true,
  callback: (result) => {
    console.log('分享结果', result)
  }
})
```

## 🛡️ 防刷机制

组件内置多重防刷机制：

1. **分享间隔限制**: 最小分享间隔1分钟
2. **每小时限制**: 每小时最多分享5次
3. **奖励冷却**: 24小时内只能获得一次奖励
4. **每日限制**: 每日最多获得1次奖励

## 🐛 常见问题

### Q: 朋友圈分享不生效？
A: 小程序中朋友圈分享需要用户手动操作，组件会提示用户点击右上角菜单进行分享。

### Q: 如何禁用某种分享方式？
A: 在 `share-config.js` 中将对应的 `enabled` 设置为 `false`。

### Q: 如何修改分享内容？
A: 在 `share-config.js` 的 `shareTypes` 中修改对应的 `title`、`description` 等字段。

### Q: 如何调整奖励机制？
A: 在 `share-config.js` 的 `shareReward` 中修改 `rewardDuration`、`cooldownPeriod` 等参数。

## 🔄 版本更新

### v1.0.0
- ✅ 基础分享功能
- ✅ 分享奖励机制
- ✅ 防刷机制
- ✅ 事件驱动架构
- ✅ 完全可配置

## 📞 技术支持

如有问题，请参考：
1. `share-config.js` 配置文件
2. 项目主文档
3. 开发进度记录.md
