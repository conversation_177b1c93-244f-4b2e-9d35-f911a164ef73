/**
 * 广告配置管理
 * 用于统一管理各种广告的开关、样式和内容
 */

// 📝 简化广告配置 - 一眼就能看懂的配置结构
const AD_CONFIG = {
  // ============== 基本开关 ==============
  globalEnabled: false,                     // 🔧 总开关：是否启用所有广告
                                          // ❌ false时，所有广告位都不会显示，包括：
                                          // • 操作前广告（保存、复制等前的广告）
                                          // • 进入小程序广告（App切换回来）
                                          // • 结果页面的常驻广告
                                          // • 个人页面的推广广告

  
  // ============== 24小时无限使用 ==============
  unlimitedAccessAfterAd: true,          // 🎯 看广告获得24小时无限解析权限
  unlimitedAccessAfterShare: true,       // 🎯 分享好友获得24小时无限解析权限
  
  // ============== 广告类型开关 ==============
  // 操作前广告（核心功能）
  operationAd: {
    enabled: true,                        // ✅ 保存/复制前显示广告
    duration: 0,                          // ⏱️ 广告时长（秒）- 0表示立即显示
    fullscreenAdDuration: 5,              // ⏱️ 全屏广告时长（秒）- 测试用5秒
    showBeforeActions: {
      saveVideo: true,                    // 保存视频前
      saveCover: true,                    // 保存封面前
      saveToAlbum: true,                  // 保存到相册前
      copyLink: true,                     // 复制链接前
      copyText: true,                     // 复制文案前
      getText: true,                      // 获取文案前
      getCover: true,                     // 获取封面前
      copyLivePhotoUrl: true,             // 复制Live Photo链接前
      testAction: true                    // 测试广告（用于主页面测试）
    }
  },
  
  // 启动页广告（开屏广告）
  splashAd: {
    enabled: true,                        // ✅ 启用启动页广告
    duration: 5,                          // ⏱️ 广告时长（秒）
    canSkip: true,                        // ⏭️ 是否可以跳过
    skipAfter: 0,                         // ⏭️ 几秒后可以跳过（0表示立即可跳过）
    // 触发场景：
    showOnFirstUse: true,                 // 🆕 第一次使用时显示
    showOnAppLaunch: true,                // 🚀 每次进入小程序时显示
    cooldownMinutes: 0                    // ⏰ 冷却时间（分钟），0表示每次都显示
  },

  // 进入小程序时的广告
  enterAd: {
    enabled: true,                        // ✅ 启用进入广告
    type: 'video',                        // 📺 广告类型：video/image
    duration: 5,                          // ⏱️ 广告时长（秒）
    canSkipAfter: 0,                      // ⏭️ 0表示不能跳过，必须等待完成
    // 触发场景：
    // • 第一次进入小程序时（在启动页广告后）
    // • 从其他App切换回微信小程序时
    showOnAppLaunch: true,                // 🚀 第一次进入时显示
    showOnAppResume: true,                // 🔄 App切换回来时显示
    showDelay: 1500                       // ⏱️ App切换后延迟显示时间（毫秒）
  },
  
  // 常驻广告位
  permanentAd: {
    enabled: true,                        // ✅ 启用常驻广告
    resultPageAd: true,                   // 结果页面广告
    profilePageAd: true,                  // 个人页面广告
    tutorialSidebarAd: true,              // 教程页面侧边栏广告
    tutorialTopAd: true                   // 教程页面顶部广告
  },
  
  // ============== 功能开关 ==============
  // 显示平台来源（配置文件控制，用户无需设置）
  showPlatformSource: false,              // ❌ 不显示具体平台名称，避免审核问题
                                         // ✅ true时显示具体平台名称（如"抖音"、"快手"）
                                         // ❌ false时显示"短视频平台"
  
  // ============== 微信广告单元ID ==============
  // 申请微信广告后填入对应ID
  wxAdIds: {
    interstitial: '',                     // 插屏广告ID
    video: ''                             // 视频广告ID
  },

  // ============== 防恶意请求配置 ==============
  rateLimitConfig: {
    maxRequestsPerMinute: 10,             // 每分钟最多请求次数
    maxRequestsPerHour: 50,               // 每小时最多请求次数
    cooldownBetweenSameUrl: 0,            // 同一URL冷却时间（秒） - 已关闭
    blockDuration: 300,                   // 超限封禁时长（秒）
    blockThresholdMultiplier: 1.5         // 触发封禁的倍数（超过每分钟限制的1.5倍）
  }
}

// 📱 广告管理器 - 简化版本
class AdManager {
  constructor() {
    this.config = AD_CONFIG
    this.hasShownEnterAd = false  // 全局状态：是否已显示过进入广告
    // 移除 operationAdStates，权限统一由 hasUnlimitedAccess() 管理
    
    // 防恶意请求机制 - 使用配置文件中的设置
    this.rateLimitConfig = this.config.rateLimitConfig
  }
  
  // 🔍 检查是否应该显示广告
  shouldShowAd(adType, actionType = '') {
    if (!this.config.globalEnabled) return false

    switch (adType) {
      case 'operation':
        if (!this.config.operationAd.enabled) return false
        return this.config.operationAd.showBeforeActions[actionType] === true
        
      case 'enter':
        return this.config.enterAd.enabled
        
      case 'result':
        return this.config.permanentAd.enabled && this.config.permanentAd.resultPageAd

      case 'profile':
        return this.config.permanentAd.enabled && this.config.permanentAd.profilePageAd

      case 'tutorial-sidebar':
        return this.config.permanentAd.enabled && this.config.permanentAd.tutorialSidebarAd

      case 'tutorial-top':
        return this.config.permanentAd.enabled && this.config.permanentAd.tutorialTopAd
        
      // case 'daily': // 已删除daily-ad-choice组件
        
      default:
        return false
    }
  }
  
  // 📋 获取广告配置
  getAdConfig(adType) {
    switch (adType) {
      case 'operation':
        return this.config.operationAd

      case 'splash':
        return this.config.splashAd

      case 'enter':
        return this.config.enterAd

      case 'result':
      case 'profile':
      case 'tutorial-sidebar':
      case 'tutorial-top':
        return this.config.permanentAd

      default:
        return {}
    }
  }
  
  // 🎯 获取平台显示名称
  getPlatformDisplayName(platform) {
    if (!this.config.showPlatformSource) {
      return '短视频平台'
    }
    
    const platformMap = {
      'douyin': 'DY',
      'kuaishou': 'KS',
      'xiaohongshu': '小红薯', 
      'bilibili': 'BL站',
      'weibo': '围脚',
      'weishi': 'WS',
      'pipix': 'PPX',
      'qishui': 'QS音乐'
    }
    
    return platformMap[platform] || '短视频平台'
  }
  
  // 🎬 显示操作前广告 (保留旧方法名兼容)
  async showInterstitialAd(actionType) {
    return this.showOperationAd(actionType)
  }

  // 🚀 检查是否应该显示启动页广告
  shouldShowSplashAd() {
    if (!this.config.globalEnabled || !this.config.splashAd.enabled) {
      console.log('[启动页广告] 广告已禁用')
      return false
    }

    // 检查冷却时间（分钟）
    const cooldownMinutes = this.config.splashAd.cooldownMinutes || 0
    if (cooldownMinutes === 0) {
      console.log('[启动页广告] 无冷却时间，每次都显示')
      return true
    }

    const lastShowTime = uni.getStorageSync('lastSplashAdShowTime') || 0
    const now = Date.now()
    const cooldownMs = cooldownMinutes * 60 * 1000

    if (now - lastShowTime < cooldownMs) {
      console.log('[启动页广告] 还在冷却期内，不显示')
      return false
    }

    console.log('[启动页广告] 冷却时间已过，可以显示')
    return true
  }



  // 🎬 显示操作前广告
  async showOperationAd(actionType) {
    return new Promise((resolve) => {
      console.log(`[权限检查] 检查操作: ${actionType}`)
      
      // 检查是否有24小时无限权限
      if (this.hasUnlimitedAccess()) {
        console.log(`[权限检查] 用户有24小时无限权限，允许操作: ${actionType}`)
        resolve(true)
        return
      }
      
      if (!this.shouldShowAd('operation', actionType)) {
        console.log(`[权限检查] 配置中禁用了广告，允许操作: ${actionType}`)
        resolve(true)
        return
      }
      
      console.log(`[权限检查] 需要显示广告才能进行操作: ${actionType}`)
      
      const config = this.getAdConfig('operation')
      
      // 触发操作广告弹窗
      uni.$emit('showOperationAd', {
        config,
        actionType,
        callback: (watchedAd) => {
          if (watchedAd) {
            console.log(`[权限检查] 用户观看了广告，操作被允许: ${actionType}`)
            resolve(true)
          } else {
            console.log(`[权限检查] 用户取消了广告，操作被拒绝: ${actionType}`)
            resolve(false)
          }
        }
      })
    })
  }

  // ⏰ 检查是否有24小时无限访问权限
  hasUnlimitedAccess() {
    // 注意：不管广告是否开启，都要检查24小时权限
    // 因为权限可能是通过分享获得的

    if (!this.config.unlimitedAccessAfterAd) {
      console.log('[权限检查] 24小时权限功能已禁用，不检查权限')
      return false
    }

    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    if (!unlimitedTime) {
      console.log('[权限检查] 无24小时权限记录')
      return false
    }
    
    const now = Date.now()
    const hoursPassed = (now - unlimitedTime) / (60 * 60 * 1000)
    
    console.log(`[权限检查] 权限获得时间: ${new Date(unlimitedTime).toLocaleString()}`)
    console.log(`[权限检查] 已过去小时数: ${hoursPassed.toFixed(2)}`)
    
    // 24小时权限已过期，清理存储
    if (hoursPassed >= 24) {
      console.log('[权限检查] 24小时权限已过期，清理存储')
      uni.removeStorageSync('unlimited_access_time')
      return false
    }
    
    console.log(`[权限检查] 24小时权限有效，剩余时间: ${(24 - hoursPassed).toFixed(2)}小时`)
    return true
  }

  // 🎁 设置24小时无限权限
  setUnlimitedAccess() {
    const now = Date.now()
    uni.setStorageSync('unlimited_access_time', now)
    console.log(`[权限设置] 已设置24小时无限权限，时间: ${new Date(now).toLocaleString()}`)
  }

  // 🚀 显示进入广告（App切换回来时）
  // 📝 注意：此方法不应影响自动粘贴等基础功能
  showEnterAd() {
    if (!this.config.globalEnabled) {
      console.log('[进入广告] globalEnabled = false，跳过显示')
      return
    }

    if (!this.shouldShowAd('enter')) return

    console.log('[进入广告] 显示进入广告')

    const config = this.getAdConfig('enter')

    // 触发进入广告事件
    uni.$emit('showEnterAd', config)
  }
  

  
  // ⚙️ 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }
  
  // ️ 清理24小时权限（调试用）
  clearUnlimitedAccess() {
    uni.removeStorageSync('unlimited_access_time')
    console.log('[权限] 24小时权限已清除')
  }

  // 🔄 重置所有广告状态（调试用）
  resetAllStates() {
    this.clearUnlimitedAccess()
    this.hasShownEnterAd = false
    console.log('[广告] 所有状态已重置')
  }
  
  // 🔍 获取当前权限状态（用于调试）
  getPermissionStatus() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    const now = Date.now()
    
    if (!unlimitedTime) {
      return { hasPermission: false, reason: '无权限记录' }
    }
    
    const hoursPassed = Math.floor((now - unlimitedTime) / (60 * 60 * 1000))
    const isExpired = hoursPassed >= 24
    
    return {
      hasPermission: !isExpired,
      unlimitedTime: new Date(unlimitedTime).toLocaleString(),
      hoursPassed,
      isExpired,
      reason: isExpired ? '权限已过期' : '权限有效'
    }
  }

  // 🛡️ 检查请求是否被速率限制
  checkRateLimit(url = '') {
    const now = Date.now()
    const config = this.rateLimitConfig
    
    // 获取请求记录
    let requestLog = uni.getStorageSync('request_log') || {
      requests: [],
      blockedUntil: 0,
      urlHistory: {}
    }
    
    // 检查是否在封禁期内
    if (requestLog.blockedUntil > now) {
      const remainingSeconds = Math.ceil((requestLog.blockedUntil - now) / 1000)
      return {
        allowed: false,
        reason: 'blocked',
        message: `请求过于频繁，请等待 ${remainingSeconds} 秒后再试`,
        remainingTime: remainingSeconds
      }
    }
    
    // 清理过期的请求记录（只保留1小时内的）
    const oneHourAgo = now - 60 * 60 * 1000
    requestLog.requests = requestLog.requests.filter(time => time > oneHourAgo)
    
    // 检查每分钟限制
    const oneMinuteAgo = now - 60 * 1000
    const recentRequests = requestLog.requests.filter(time => time > oneMinuteAgo)
    
    if (recentRequests.length >= config.maxRequestsPerMinute) {
      return {
        allowed: false,
        reason: 'minute_limit',
        message: `每分钟最多 ${config.maxRequestsPerMinute} 次请求，请稍后再试`,
        currentCount: recentRequests.length,
        limit: config.maxRequestsPerMinute
      }
    }
    
    // 检查每小时限制
    if (requestLog.requests.length >= config.maxRequestsPerHour) {
      return {
        allowed: false,
        reason: 'hour_limit',
        message: `每小时最多 ${config.maxRequestsPerHour} 次请求，请稍后再试`,
        currentCount: requestLog.requests.length,
        limit: config.maxRequestsPerHour
      }
    }
    
    // 检查同一URL的冷却时间（只在冷却时间大于0时检查）
    if (url && config.cooldownBetweenSameUrl > 0) {
      const urlHash = this.hashUrl(url)
      const lastRequestTime = requestLog.urlHistory[urlHash]
      
      if (lastRequestTime && (now - lastRequestTime) < config.cooldownBetweenSameUrl * 1000) {
        const remainingSeconds = Math.ceil((config.cooldownBetweenSameUrl * 1000 - (now - lastRequestTime)) / 1000)
        return {
          allowed: false,
          reason: 'same_url_cooldown',
          message: `同一视频需要等待 ${remainingSeconds} 秒才能重新解析`,
          remainingTime: remainingSeconds
        }
      }
    }
    
    return {
      allowed: true,
      reason: 'ok',
      message: '请求允许'
    }
  }
  
  // 📝 记录请求
  recordRequest(url = '') {
    const now = Date.now()
    let requestLog = uni.getStorageSync('request_log') || {
      requests: [],
      blockedUntil: 0,
      urlHistory: {}
    }
    
    // 记录请求时间
    requestLog.requests.push(now)
    
    // 记录URL历史
    if (url) {
      const urlHash = this.hashUrl(url)
      requestLog.urlHistory[urlHash] = now
      
      // 清理旧的URL记录（只保留24小时内的）
      const oneDayAgo = now - 24 * 60 * 60 * 1000
      Object.keys(requestLog.urlHistory).forEach(hash => {
        if (requestLog.urlHistory[hash] < oneDayAgo) {
          delete requestLog.urlHistory[hash]
        }
      })
    }
    
    // 检查是否需要临时封禁
    const config = this.rateLimitConfig
    const oneMinuteAgo = now - 60 * 1000
    const recentRequests = requestLog.requests.filter(time => time > oneMinuteAgo)
    
    if (recentRequests.length > config.maxRequestsPerMinute * 1.5) {
      // 超过限制的1.5倍，临时封禁
      requestLog.blockedUntil = now + config.blockDuration * 1000
      console.log('[防恶意] 检测到异常请求频率，临时封禁用户')
    }
    
    // 保存请求记录
    uni.setStorageSync('request_log', requestLog)
  }
  
  // 🔤 生成URL哈希（简单哈希函数）
  hashUrl(url) {
    let hash = 0
    if (url.length === 0) return hash.toString()
    
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString()
  }
  
  // 📊 获取速率限制状态
  getRateLimitStatus() {
    const now = Date.now()
    const requestLog = uni.getStorageSync('request_log') || {
      requests: [],
      blockedUntil: 0,
      urlHistory: {}
    }
    
    const oneMinuteAgo = now - 60 * 1000
    const oneHourAgo = now - 60 * 60 * 1000
    
    const requestsThisMinute = requestLog.requests.filter(time => time > oneMinuteAgo).length
    const requestsThisHour = requestLog.requests.filter(time => time > oneHourAgo).length
    
    return {
      requestsThisMinute,
      requestsThisHour,
      maxPerMinute: this.rateLimitConfig.maxRequestsPerMinute,
      maxPerHour: this.rateLimitConfig.maxRequestsPerHour,
      isBlocked: requestLog.blockedUntil > now,
      blockedUntil: requestLog.blockedUntil > now ? new Date(requestLog.blockedUntil).toLocaleString() : null
    }
  }
  
  // 🗑️ 清理速率限制记录
  clearRateLimitHistory() {
    uni.removeStorageSync('request_log')
    console.log('[防恶意] 请求记录已清理')
  }
}

// 创建全局广告管理器实例
const adManager = new AdManager()

export default adManager
export { AD_CONFIG }
