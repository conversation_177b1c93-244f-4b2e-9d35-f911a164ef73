<template>
  <view v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <!-- 弹窗内容 -->
    <view class="dialog-content" @click.stop>
      <!-- 顶部小三角 -->
      <view class="dialog-triangle"></view>

      <view class="dialog-title">{{ title }}</view>

      <!-- 步骤1：点击更多 -->
      <view class="step-item">
        <text class="step-number">1.</text>
        <view class="step-content">
          <text class="step-text">点击右上角"更多"</text>
          <image
            class="step-icon"
            src="/static/images/guide/more-dots.png"
            mode="aspectFit"
            @error="handleImageError"
          />
        </view>
      </view>

      <!-- 步骤2：添加到我的小程序 -->
      <view class="step-item">
        <text class="step-number">2.</text>
        <text class="step-text">点击"添加到我的小程序"</text>
      </view>

      <!-- 添加小程序图标展示 -->
      <view class="miniprogram-icons">
        <image
          class="miniprogram-guide-image"
          src="/static/images/guide/add-to-miniprogram.jpg"
          mode="widthFix"
          @error="handleImageError"
        />
      </view>

      <!-- 步骤3：微信首页下拉 -->
      <view class="step-item">
        <text class="step-number">3.</text>
        <text class="step-text">微信首页下拉，即可快速进入</text>
      </view>

      <view class="dialog-button" @click="handleConfirm">
        <text class="button-text">我知道了</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FirstUseDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '下次可以这样找到我'
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm')
    },

    handleOverlayClick() {
      // 点击遮罩层也可以关闭
      this.$emit('confirm')
    },

    handleImageError() {
      console.log('[首次使用弹窗] 引导图片加载失败，使用文字说明')
    }
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 20rpx;
  padding-top: calc(var(--status-bar-height, 44px) + 5rpx); /* 动态状态栏高度 + 最小间距 */
  padding-right: 40rpx; /* 右侧间距 */
}

/* 弹窗内容 - 缩小尺寸，响应式适配 */
.dialog-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx 30rpx 30rpx;
  max-width: 480rpx; /* 适合右侧显示的宽度 */
  width: 85%;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
  position: relative;
  margin: 0; /* 取消居中，靠右显示 */
}

/* 顶部小三角 - 紧贴右上角三个点 */
.dialog-triangle {
  position: absolute;
  top: -20rpx;
  right: 80rpx; /* 调整位置，更好地指向右上角三个点 */
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 20rpx solid #FFFFFF;
}

.dialog-title {
  font-size: 32rpx; /* 缩小字体 */
  font-weight: 600;
  color: #333333;
  text-align: left;
  margin-bottom: 30rpx; /* 缩小间距 */
  line-height: 1.4;
}

/* 步骤项 */
.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx; /* 缩小间距 */
  min-height: 44rpx; /* 缩小高度 */
}

.step-number {
  font-size: 28rpx; /* 缩小字体 */
  color: #333333;
  font-weight: 500;
  margin-right: 10rpx; /* 缩小间距 */
  min-width: 36rpx; /* 缩小宽度 */
}

.step-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-text {
  font-size: 28rpx; /* 缩小字体 */
  color: #333333;
  line-height: 1.4;
  margin-right: 8rpx; /* 与图标距离更近 */
}

.step-icon {
  width: 50rpx; /* 缩小图标 */
  height: 32rpx; /* 缩小图标 */
}

/* 小程序图标展示区域 */
.miniprogram-icons {
  margin: 20rpx 0; /* 缩小间距 */
  padding: 0 15rpx; /* 缩小内边距 */
  text-align: center;
}

.miniprogram-guide-image {
  width: 100%;
  max-width: 400rpx; /* 缩小图片 */
  border-radius: 6rpx; /* 缩小圆角 */
}

/* 确认按钮 */
.dialog-button {
  background: #007AFF;
  border-radius: 12rpx; /* 缩小圆角 */
  height: 80rpx; /* 缩小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx; /* 缩小间距 */
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.dialog-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.button-text {
  color: #FFFFFF;
  font-size: 30rpx; /* 缩小字体 */
  font-weight: 600;
}

/* 动画效果 */
.dialog-overlay {
  animation: fadeIn 0.3s ease-out;
}

.dialog-content {
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .dialog-content {
    padding: 30rpx 25rpx 25rpx; /* 进一步缩小 */
    max-width: 420rpx; /* 小屏幕下更小 */
    margin: 0; /* 小屏幕下也靠右显示 */
  }

  .dialog-triangle {
    right: 70rpx; /* 小屏幕下调整位置 */
  }

  .dialog-title {
    font-size: 28rpx; /* 进一步缩小 */
    margin-bottom: 24rpx;
  }

  .step-number,
  .step-text {
    font-size: 26rpx; /* 进一步缩小 */
  }

  .step-icon {
    width: 44rpx; /* 进一步缩小 */
    height: 28rpx;
  }

  .button-text {
    font-size: 28rpx; /* 进一步缩小 */
  }

  .dialog-button {
    height: 72rpx; /* 进一步缩小 */
  }

  .miniprogram-guide-image {
    max-width: 350rpx; /* 小屏幕下更小 */
  }
}

/* 大屏幕适配 */
@media (min-width: 1000rpx) {
  .dialog-content {
    margin: 0; /* 大屏幕下也靠右显示 */
    max-width: 520rpx; /* 大屏幕下稍微增大宽度 */
  }

  .dialog-triangle {
    right: 90rpx; /* 大屏幕下调整箭头位置 */
  }
}
</style>