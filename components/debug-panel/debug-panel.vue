<template>
  <view v-if="showDebugPanel" class="debug-panel">
    <view class="debug-header">
      <text class="debug-title">🔧 调试面板</text>
    </view>
    
    <scroll-view scroll-y class="debug-content">
      <!-- 关闭按钮移到内容区域顶部，避免与微信官方按钮冲突 -->
      <view class="close-button-section">
        <text class="debug-close" @click="closeDebugPanel">✕ 关闭调试面板</text>
      </view>
      <!-- 权限状态检查 -->
      <view class="debug-section">
        <text class="section-title">📱 权限状态</text>
        <view class="info-item">
          <text class="info-label">相册权限：</text>
          <text class="info-value" :class="getPermissionClass(albumPermission)">
            {{ albumPermission }}
          </text>
          <button v-if="albumPermission !== '已授权'" 
                  class="mini-btn" 
                  @click="checkAlbumPermission">
            重新检查
          </button>
        </view>
      </view>

      <!-- 网络状态检查 -->
      <view class="debug-section">
        <text class="section-title">🌐 网络状态</text>
        <view class="info-item">
          <text class="info-label">网络类型：</text>
          <text class="info-value">{{ networkType }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">网络连接：</text>
          <text class="info-value" :class="getNetworkClass(isConnected)">
            {{ isConnected ? '已连接' : '未连接' }}
          </text>
        </view>
      </view>

      <!-- 系统信息 -->
      <view class="debug-section">
        <text class="section-title">📋 系统信息</text>
        <view class="info-item">
          <text class="info-label">平台：</text>
          <text class="info-value">{{ systemInfo.platform }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">微信版本：</text>
          <text class="info-value">{{ systemInfo.version }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">系统版本：</text>
          <text class="info-value">{{ systemInfo.system }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">小程序版本：</text>
          <text class="info-value">{{ systemInfo.SDKVersion }}</text>
        </view>
      </view>

      <!-- 错误日志 -->
      <view class="debug-section">
        <view class="section-header">
          <text class="section-title">📝 错误日志</text>
          <view class="section-actions">
            <button class="copy-btn" @click="copyLogs">📋 复制</button>
            <button class="clear-btn" @click="clearLogs">🗑️ 清空</button>
          </view>
        </view>
        <view v-if="errorLogs.length === 0" class="no-logs">
          <text>暂无错误日志</text>
        </view>
        <view v-else class="log-list">
          <view v-for="(log, index) in errorLogs" :key="index" class="log-item">
            <text class="log-time">{{ formatTime(log.time) }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </view>
      </view>

      <!-- 测试按钮 -->
      <view class="debug-section">
        <text class="section-title">🧪 功能测试</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testAlbumPermission">
            测试相册权限
          </button>
          <button class="test-btn" @click="testNetworkAccess">
            测试网络访问
          </button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'DebugPanel',
  data() {
    return {
      showDebugPanel: false,
      albumPermission: '检查中...',
      networkType: '检查中...',
      isConnected: false,
      systemInfo: {},
      errorLogs: []
    }
  },
  
  mounted() {
    this.initDebugPanel()
    this.setupErrorHandler()
  },
  
  methods: {
    // 初始化调试面板
    async initDebugPanel() {
      await this.checkSystemInfo()
      await this.checkPermissions()
      await this.checkNetworkStatus()
    },
    
    // 设置错误处理器
    setupErrorHandler() {
      // 监听全局错误
      const originalError = console.error
      console.error = (...args) => {
        this.addErrorLog(args.join(' '))
        originalError.apply(console, args)
      }
    },
    
    // 显示调试面板
    showDebug() {
      this.showDebugPanel = true
      this.initDebugPanel()
    },
    
    // 关闭调试面板
    closeDebugPanel() {
      this.showDebugPanel = false
    },
    
    // 检查系统信息
    async checkSystemInfo() {
      try {
        this.systemInfo = uni.getSystemInfoSync()
        this.addDebugLog('系统信息获取成功')
      } catch (error) {
        this.addErrorLog('获取系统信息失败: ' + error.message)
      }
    },
    
    // 检查权限状态
    async checkPermissions() {
      await this.checkAlbumPermission()
    },
    
    // 检查相册权限
    async checkAlbumPermission() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.getSetting({
            success: resolve,
            fail: reject
          })
        })
        
        const albumAuth = res.authSetting['scope.writePhotosAlbum']
        if (albumAuth === true) {
          this.albumPermission = '已授权'
        } else if (albumAuth === false) {
          this.albumPermission = '已拒绝'
        } else {
          this.albumPermission = '未询问'
        }
        
        this.addDebugLog(`相册权限状态: ${this.albumPermission}`)
      } catch (error) {
        this.albumPermission = '检查失败'
        this.addErrorLog('检查相册权限失败: ' + error.message)
      }
    },
    
    // 检查网络状态
    async checkNetworkStatus() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.getNetworkType({
            success: resolve,
            fail: reject
          })
        })
        
        this.networkType = res.networkType
        this.isConnected = res.networkType !== 'none'
        this.addDebugLog(`网络状态: ${res.networkType}`)
      } catch (error) {
        this.networkType = '检查失败'
        this.isConnected = false
        this.addErrorLog('检查网络状态失败: ' + error.message)
      }
    },
    
    // 测试相册权限
    async testAlbumPermission() {
      try {
        uni.showLoading({ title: '测试中...' })
        
        // 尝试授权
        await new Promise((resolve, reject) => {
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: resolve,
            fail: reject
          })
        })
        
        uni.hideLoading()
        uni.showToast({
          title: '相册权限正常',
          icon: 'success'
        })
        
        this.addDebugLog('相册权限测试通过')
        await this.checkAlbumPermission() // 重新检查状态
        
      } catch (error) {
        uni.hideLoading()
        uni.showModal({
          title: '权限测试失败',
          content: error.errMsg || error.message,
          showCancel: false
        })
        this.addErrorLog('相册权限测试失败: ' + (error.errMsg || error.message))
      }
    },
    
    // 测试网络访问
    async testNetworkAccess() {
      try {
        uni.showLoading({ title: '测试中...' })
        
        const res = await new Promise((resolve, reject) => {
          uni.request({
            url: 'https://api.next.bspapp.com/ping',
            timeout: 5000,
            success: resolve,
            fail: reject
          })
        })
        
        uni.hideLoading()
        uni.showToast({
          title: '网络访问正常',
          icon: 'success'
        })
        
        this.addDebugLog('网络访问测试通过')
        
      } catch (error) {
        uni.hideLoading()
        uni.showModal({
          title: '网络测试失败',
          content: error.errMsg || error.message,
          showCancel: false
        })
        this.addErrorLog('网络访问测试失败: ' + (error.errMsg || error.message))
      }
    },
    
    // 添加错误日志
    addErrorLog(message) {
      this.errorLogs.unshift({
        time: Date.now(),
        message: message
      })
      
      // 只保留最近50条日志
      if (this.errorLogs.length > 50) {
        this.errorLogs.splice(50)
      }
    },
    
    // 添加调试日志
    addDebugLog(message) {
      console.log('[调试面板]', message)
    },
    
    // 复制日志
    copyLogs() {
      if (this.errorLogs.length === 0) {
        uni.showToast({
          title: '暂无日志可复制',
          icon: 'none'
        })
        return
      }
      
      const logText = this.errorLogs.map(log => {
        return `[${this.formatTime(log.time)}] ${log.message}`
      }).join('\n')
      
      uni.setClipboardData({
        data: logText,
        success: () => {
          uni.showToast({
            title: '日志已复制到剪贴板',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'error'
          })
        }
      })
    },
    
    // 清空日志
    clearLogs() {
      this.errorLogs = []
      uni.showToast({
        title: '日志已清空',
        icon: 'success'
      })
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleTimeString()
    },
    
    // 获取权限状态样式
    getPermissionClass(permission) {
      if (permission === '已授权') return 'status-success'
      if (permission === '已拒绝') return 'status-error'
      return 'status-warning'
    },
    
    // 获取网络状态样式
    getNetworkClass(isConnected) {
      return isConnected ? 'status-success' : 'status-error'
    }
  }
}
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  flex-direction: column;
}

.debug-header {
  background: #2c3e50;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 只留上部间距，不放置关闭按钮 */
  padding-top: 40rpx;
}

.debug-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 新增关闭按钮区域样式 */
.close-button-section {
  background: #34495e;
  padding: 15rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.debug-close {
  color: #ffffff;
  font-size: 28rpx;
  background: #e74c3c;
  border-radius: 8rpx;
  padding: 15rpx 30rpx;
  display: inline-block;
  cursor: pointer;
  /* 设置为按钮样式，更容易点击 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s;
}

.debug-close:active {
  background: #c0392b;
}

.debug-content {
  flex: 1;
  background: #ffffff;
  padding: 20rpx;
}

.debug-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15rpx;
  display: block;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.section-actions {
  display: flex;
  gap: 10rpx;
}

.copy-btn {
  background: #27ae60;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  margin-left: 5rpx;
}

.clear-btn {
  background: #e74c3c;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  margin-left: 5rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 24rpx;
  flex: 1;
}

.status-success {
  color: #27ae60;
}

.status-error {
  color: #e74c3c;
}

.status-warning {
  color: #f39c12;
}

.mini-btn {
  background: #3498db;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  margin-left: 10rpx;
}

.no-logs {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.log-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.log-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
  margin-bottom: 10rpx;
}

.log-time {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 5rpx;
  display: block;
}

.log-message {
  font-size: 22rpx;
  color: #333;
  word-break: break-all;
}

.clear-btn {
  background: #e74c3c;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  margin-top: 15rpx;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.test-btn {
  background: #27ae60;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  flex: 1;
  min-width: 200rpx;
}
</style>