<template>
  <view v-if="shouldShowAd" class="profile-ad" @click="onAdClick">
    <!-- 广告标识 -->
    <view class="ad-label">
      <text class="ad-text">广告</text>
    </view>
    
    <!-- 广告内容 -->
    <view class="ad-content">
      <!-- 广告图片 -->
      <view class="ad-image">
        <text class="ad-emoji">🚀</text>
      </view>
      
      <!-- 广告信息 -->
      <view class="ad-info">
        <!-- 广告标题 -->
        <text class="ad-title">墨影去水印专业版</text>

        <!-- 广告描述 -->
        <text class="ad-description">解锁更多功能，享受无限制解析</text>

        <!-- 广告底部信息 -->
        <view class="ad-footer">
          <view class="ad-brand">
            <text class="brand-name">墨影去水印</text>
          </view>
          <view class="ad-action">
            <text class="action-text">立即体验</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 关闭按钮 -->
    <view class="ad-close" @click.stop="closeAd" v-if="showClose">
      <text class="close-icon">×</text>
    </view>
  </view>
</template>

<script>
import adManager from '../ad-config.js'

export default {
  name: 'ProfileAd',
  props: {
    showClose: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      shouldShow: true
    }
  },
  
  computed: {
    // 🔧 响应 globalEnabled 配置
    shouldShowAd() {
      return this.shouldShow && adManager.shouldShowAd('profile')
    }
  },
  
  methods: {
    onAdClick() {
      // 广告点击事件
      console.log('个人中心广告被点击')
      
      // 触发点击事件
      this.$emit('adClick', {
        adType: 'profile'
      })
      
      // 显示广告详情
      uni.showModal({
        title: '墨影去水印专业版',
        content: '解锁更多高级功能：\n• 批量解析\n• 高清下载\n• 无广告体验\n• 优先客服支持',
        showCancel: true,
        cancelText: '知道了',
        confirmText: '了解更多',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },
    
    closeAd() {
      this.shouldShow = false
      this.$emit('adClose', {
        adType: 'profile'
      })
    }
  }
}
</script>

<style scoped>
.profile-ad {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #F0F0F0;
  position: relative;
  transition: transform 0.2s ease;
}

.profile-ad:active {
  transform: scale(0.98);
}

.ad-label {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  z-index: 10;
}

.ad-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

.ad-content {
  display: flex;
  padding: 20rpx;
  gap: 20rpx;
}

.ad-image {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ad-emoji {
  font-size: 60rpx;
}

.ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.ad-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ad-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ad-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #F0F0F0;
}

.ad-brand {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.brand-name {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ad-action {
  padding: 12rpx 24rpx;
  background: linear-gradient(45deg, #007AFF, #5AC8FA);
  border-radius: 20rpx;
}

.action-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.ad-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.close-icon {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .ad-content {
    flex-direction: column;
  }
  
  .ad-image {
    width: 100%;
    height: 180rpx;
  }
  
  .ad-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .ad-action {
    align-self: stretch;
    text-align: center;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-ad {
  animation: fadeInUp 0.3s ease-out;
}
</style>
