/**
 * 分享功能配置管理
 * 用于统一管理分享功能的开关、奖励机制和配置参数
 * 设计理念：完全可插拔，类似广告框架的架构
 */

// 导入广告管理器，用于检查广告状态
import adManager from './ad-config.js'

// 📝 分享功能配置 - 一眼就能看懂的配置结构
const SHARE_CONFIG = {
  // ============== 基本开关 ==============
  globalEnabled: false,                     // 🔧 总开关：是否启用分享功能
                                          // ❌ false时，所有分享入口都不会显示，包括：
                                          // • 结果页面的分享按钮
                                          // • 个人中心的分享推广
                                          // • 分享奖励机制

  testMode: false,                          // 🧪 测试模式：开发环境中模拟分享成功
                                          // ✅ true时，直接模拟分享成功并获得24小时权限
                                          // 🚀 发布前改为false，使用真实微信分享
                                          // ⚠️ 当前true，开发环境模拟分享获得权限
                                          // ✅ true时，直接模拟分享成功
                                          // 🚀 发布前改为false，使用真实微信分享
                                          // ⚠️ 当前false，会调用真实的微信分享 API

  // ============== 强制分享配置 ==============
  forceShare: {
    enabled: false,                        // 🔧 是否启用强制分享功能
                                          // ✅ true时，在没有广告的情况下，用户必须分享才能进行操作
                                          // • 保存到相册需要分享
                                          // • 复制链接需要分享
                                          // • 获取文案需要分享
                                          // • 获取封面需要分享

    requireShareForActions: [             // 📋 需要强制分享的操作列表
      'saveToAlbum',                      // 保存到相册
      'copyLink',                         // 复制链接
      'getText',                          // 获取文案
      'getCover',                         // 获取封面
      'copyLivePhotoUrl'                  // 复制Live Photo链接
    ],

    dialogConfig: {
      title: '需要分享才能继续',
      content: '为了维持服务运营，请分享小程序给好友后继续使用',
      shareButtonText: '去分享',
      cancelButtonText: '取消',
      showCancel: true
    }
  },

  
  // ============== 分享权限配置 ==============
  sharePermission: {
    enabled: true,                        // ✅ 启用分享权限
    duration: 24 * 60 * 60 * 1000,      // ⏰ 权限时长：24小时
    description: '分享成功后获得24小时无限解析权限'
  },
  
  // ============== 分享入口配置 ==============
  shareEntries: {
    resultPage: {
      enabled: true,                      // ✅ 结果页面分享按钮
      position: 'bottom',                 // 📍 位置：bottom/top/floating
      showRewardHint: true,               // 💡 显示奖励提示
      description: '解析结果页面的分享按钮'
    },
    profilePage: {
      enabled: true,                      // ✅ 个人中心分享推广
      showRewardHint: true,               // 💡 显示奖励提示
      description: '个人中心页面的分享推广'
    },
    floatingButton: {
      enabled: false,                     // ❌ 悬浮分享按钮（可选）
      position: 'right-bottom',           // 📍 位置：right-bottom/left-bottom
      description: '全局悬浮分享按钮'
    },
    forceShare: {
      enabled: true,                      // ✅ 强制分享入口
      showRewardHint: true,               // 💡 显示奖励提示
      description: '强制分享弹窗入口'
    },
    tutorialSidebar: {
      enabled: true,                      // ✅ 教程页面侧边栏分享
      showRewardHint: true,               // 💡 显示奖励提示
      description: '教程页面侧边栏的分享推广'
    }
  },
  
  // ============== 分享方式配置 ==============
  shareTypes: {
    wechatFriend: {
      enabled: true,                      // ✅ 微信好友分享
      title: 'MarkEraser - 短视频去水印工具',
      description: '免费去除抖音、快手、小红书等平台视频水印',
      imageUrl: '/static/logo.png'        // 分享图片
    },
    wechatMoments: {
      enabled: true,                      // ✅ 朋友圈分享
      title: 'MarkEraser - 短视频去水印工具',
      description: '免费去除抖音、快手、小红书等平台视频水印',
      imageUrl: '/static/logo.png'
    },
    copyLink: {
      enabled: true,                      // ✅ 复制链接分享
      linkTemplate: '推荐一个好用的短视频去水印工具：MarkEraser，支持抖音、快手、小红书等多个平台！',
      description: '复制分享链接到其他平台'
    }
  },
  
  // ============== 防刷机制配置 ==============
  antiAbuse: {
    enabled: false,                       // ❌ 禁用防刷机制，鼓励用户多分享
    minShareInterval: 0,                  // ⏱️ 无分享间隔限制
    maxSharesPerHour: 999,                // 📊 无每小时分享次数限制
    requireRealShare: true,               // 🔍 要求真实分享（检测分享回调）
    description: '鼓励用户多分享，不设置限制'
  },
  
  // ============== 统计配置 ==============
  statistics: {
    enabled: true,                        // ✅ 启用分享统计
    trackShareCount: true,                // 📊 统计分享次数
    trackRewardCount: true,               // 🎁 统计奖励发放次数
    trackConversion: true,                // 📈 统计转化率
    description: '分享功能使用统计'
  }
}

// 📱 分享管理器 - 类似广告管理器的设计
class ShareManager {
  constructor() {
    this.config = SHARE_CONFIG
    this.shareStates = {
      lastShareTime: 0,                   // 上次分享时间
      totalShares: 0                      // 总分享次数
    }
    
    // 加载本地存储的状态
    this.loadShareStates()
  }
  
  // 🔍 检查是否应该显示分享功能
  shouldShowShare(entryType) {
    if (!this.config.globalEnabled) return false

    const entryConfig = this.config.shareEntries[entryType]
    return entryConfig && entryConfig.enabled
  }

  // 🔒 检查是否需要强制分享
  shouldForceShare(actionType) {
    // 如果分享功能未启用，不强制分享
    if (!this.config.globalEnabled) return false

    // 如果强制分享功能未启用，不强制分享
    if (!this.config.forceShare.enabled) return false

    // 检查该操作是否在强制分享列表中
    return this.config.forceShare.requireShareForActions.includes(actionType)
  }

  // 🚫 检查操作是否被阻止（需要分享）
  checkActionPermission(actionType) {
    console.log(`[强制分享] 检查操作权限: ${actionType}`)

    // 如果不需要强制分享，直接允许
    if (!this.shouldForceShare(actionType)) {
      console.log(`[强制分享] 操作 ${actionType} 不需要强制分享`)
      return { allowed: true, reason: '操作允许' }
    }

    // 检查是否有有效的分享权限
    const hasAccess = this.hasUnlimitedAccess()
    console.log(`[强制分享] 检查权限状态: hasUnlimitedAccess = ${hasAccess}`)

    if (hasAccess) {
      console.log(`[强制分享] 操作 ${actionType} 已有权限，允许执行`)
      return { allowed: true, reason: '已有分享权限' }
    }

    // 需要分享才能继续
    console.log(`[强制分享] 操作 ${actionType} 需要分享权限`)
    return {
      allowed: false,
      reason: '需要分享才能继续操作',
      actionType: actionType
    }
  }
  
  // 📋 获取分享配置
  getShareConfig(entryType) {
    return this.config.shareEntries[entryType] || {}
  }
  
  // 🎁 检查是否可以获得分享奖励
  canGetSharePermission() {
    if (!this.config.globalEnabled || !this.config.sharePermission.enabled) {
      return { allowed: false, reason: '分享功能已禁用' }
    }

    return { allowed: true, reason: '分享即可获得24小时权限' }
  }
  
  // 🚀 显示分享弹窗
  showShareDialog(options = {}) {
    if (!this.shouldShowShare(options.entryType || 'resultPage')) {
      console.log('[分享] 分享功能已禁用')
      return
    }

    // 检查防刷机制
    if (!this.checkAntiAbuse()) {
      return
    }

    console.log('[分享] 显示分享弹窗')

    // 触发分享弹窗事件
    uni.$emit('showShareDialog', {
      config: this.config,
      entryType: options.entryType || 'resultPage',
      showRewardHint: options.showRewardHint !== false,
      callback: (shareResult) => {
        if (shareResult.success) {
          this.handleShareSuccess(shareResult)
        }
        // 执行回调
        if (options.callback) {
          options.callback(shareResult)
        }
      }
    })
  }

  // 🔒 显示强制分享弹窗（复用广告弹窗）
  showForceShareDialog(actionType, callback) {
    if (!this.shouldForceShare(actionType)) {
      console.log('[强制分享] 该操作不需要强制分享')
      if (callback) callback({ allowed: true })
      return
    }

    console.log('[强制分享] 显示强制分享弹窗（复用广告弹窗），操作类型:', actionType)

    // 复用广告弹窗，根据广告开启状态决定显示的按钮
    uni.$emit('showOperationAd', {
      config: {
        // 如果广告开启，显示广告按钮；否则不显示
        showWatchAdButton: adManager.shouldShowAd('operation', actionType),
        // 分享功能开启时显示分享按钮
        showShareButton: true
      },
      actionType: actionType,
      callback: (adResult) => {
        // 将广告弹窗的回调结果转换为强制分享的格式
        if (adResult) {
          // 广告弹窗返回 true 表示用户完成了操作（观看广告或分享成功）
          if (callback) callback({ allowed: true, shared: true })
        } else {
          // 广告弹窗返回 false 表示用户取消了操作
          if (callback) callback({ allowed: false, reason: '用户取消操作' })
        }
      }
    })
  }
  
  // ✅ 处理分享成功
  handleShareSuccess(shareResult) {
    console.log('[分享] 分享成功', shareResult)

    // 直接设置24小时权限
    const now = Date.now()
    uni.setStorageSync('unlimited_access_time', now)
    console.log('[分享] 分享成功，设置24小时权限:', now)

    // 更新分享统计
    this.updateShareStats(shareResult.shareType)

    // 显示成功提示
    uni.showToast({
      title: '分享成功！',
      icon: 'success',
      duration: 2000
    })
  }
  


  // 🔍 检查当前权限状态（与广告系统保持一致）
  hasUnlimitedAccess() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    if (!unlimitedTime) {
      return false
    }

    const now = Date.now()
    const hoursPassed = (now - unlimitedTime) / (60 * 60 * 1000)

    // 24小时权限已过期，清理存储
    if (hoursPassed >= 24) {
      uni.removeStorageSync('unlimited_access_time')
      return false
    }

    return true
  }

  // 📊 获取权限剩余时间
  getRemainingAccessTime() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    if (!unlimitedTime) {
      return 0
    }

    const now = Date.now()
    const remainingTime = (unlimitedTime + 24 * 60 * 60 * 1000) - now

    return Math.max(0, remainingTime)
  }
  
  // 🛡️ 检查防刷机制
  checkAntiAbuse() {
    if (!this.config.antiAbuse.enabled) return true
    
    const now = Date.now()
    const antiAbuseConfig = this.config.antiAbuse
    
    // 检查分享间隔
    const timeSinceLastShare = now - this.shareStates.lastShareTime
    if (timeSinceLastShare < antiAbuseConfig.minShareInterval) {
      const remainingSeconds = Math.ceil((antiAbuseConfig.minShareInterval - timeSinceLastShare) / 1000)
      uni.showToast({
        title: `请等待 ${remainingSeconds} 秒后再分享`,
        icon: 'none',
        duration: 2000
      })
      return false
    }
    
    // 检查每小时分享次数
    const oneHourAgo = now - 60 * 60 * 1000
    if (this.shareStates.lastShareTime > oneHourAgo && 
        this.shareStates.dailyShareCount >= antiAbuseConfig.maxSharesPerHour) {
      uni.showToast({
        title: `每小时最多分享 ${antiAbuseConfig.maxSharesPerHour} 次`,
        icon: 'none',
        duration: 2000
      })
      return false
    }
    
    return true
  }
  
  // 📊 更新分享统计
  updateShareStats(shareType) {
    const now = Date.now()
    
    this.shareStates.lastShareTime = now
    this.shareStates.totalShares++

    this.saveShareStates()

    console.log(`[分享] 统计已更新: ${shareType}, 总分享次数: ${this.shareStates.totalShares}`)
  }
  
  // 💾 保存分享状态
  saveShareStates() {
    uni.setStorageSync('share_states', this.shareStates)
  }
  
  // 📖 加载分享状态
  loadShareStates() {
    const savedStates = uni.getStorageSync('share_states')
    if (savedStates) {
      this.shareStates = { ...this.shareStates, ...savedStates }
    }
    
    // 检查是否需要重置每日计数
    this.checkDailyReset()
  }
  
  // 🔄 检查每日重置
  checkDailyReset() {
    const now = Date.now()
    const lastResetDate = uni.getStorageSync('share_last_reset_date') || 0
    const today = new Date(now).toDateString()
    const lastResetDateStr = new Date(lastResetDate).toDateString()
    
    if (today !== lastResetDateStr) {
      // 重置每日计数
      this.shareStates.dailyShareCount = 0
      this.shareStates.dailyRewardCount = 0
      uni.setStorageSync('share_last_reset_date', now)
      this.saveShareStates()
      console.log('[分享] 每日计数已重置')
    }
  }
  
  // 📊 获取分享统计
  getShareStats() {
    return {
      ...this.shareStates,
      canGetPermission: this.canGetSharePermission().allowed
    }
  }
  
  // 🗑️ 清理分享数据（调试用）
  clearShareData() {
    uni.removeStorageSync('share_states')
    uni.removeStorageSync('share_last_reset_date')
    // 清理分享统计数据
    const shareTypes = ['wechatFriend', 'wechatMoments', 'copyLink']
    shareTypes.forEach(type => {
      uni.removeStorageSync(`share_stats_${type}`)
    })

    this.shareStates = {
      lastShareTime: 0,
      totalShares: 0
    }
    console.log('[分享] 分享数据已清理')
  }

  // 🔄 重置所有分享状态（调试用）
  resetAllShareStates() {
    this.clearShareData()
    // 注意：不清理 unlimited_access_time，因为这是与广告系统共享的权限
    console.log('[分享] 所有分享状态已重置')
  }

  // 📊 获取详细的分享统计报告
  getDetailedShareStats() {
    const stats = this.getShareStats()
    const shareTypes = ['wechatFriend', 'wechatMoments', 'copyLink']
    const typeStats = {}

    shareTypes.forEach(type => {
      const typeData = uni.getStorageSync(`share_stats_${type}`) || { count: 0, lastTime: 0 }
      typeStats[type] = typeData
    })

    return {
      ...stats,
      typeBreakdown: typeStats,
      remainingAccessTime: this.getRemainingAccessTime(),
      hasCurrentAccess: this.hasUnlimitedAccess()
    }
  }

  // 🎯 检查分享功能是否可用
  isShareFeatureAvailable() {
    return this.config.globalEnabled
  }

  // ⚙️ 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    console.log('[分享] 配置已更新', newConfig)
  }

  // 🔧 获取分享按钮配置（供UI组件使用）
  getShareButtonConfig(entryType) {
    if (!this.shouldShowShare(entryType)) {
      return { show: false }
    }

    const entryConfig = this.getShareConfig(entryType)
    const permissionCheck = this.canGetSharePermission()

    return {
      show: true,
      showRewardHint: entryConfig.showRewardHint && permissionCheck.allowed,
      rewardText: permissionCheck.allowed ? '分享获得24小时无限权限' : permissionCheck.reason,
      position: entryConfig.position || 'bottom'
    }
  }

  // 🐛 调试方法：检查权限状态
  debugPermissionStatus() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    const hasAccess = this.hasUnlimitedAccess()
    const remainingTime = this.getRemainingAccessTime()

    console.log('=== 权限状态调试 ===')
    console.log('unlimited_access_time:', unlimitedTime)
    console.log('hasUnlimitedAccess():', hasAccess)
    console.log('剩余时间(小时):', remainingTime / (60 * 60 * 1000))
    console.log('当前时间:', Date.now())

    if (unlimitedTime) {
      console.log('权限设置时间:', new Date(unlimitedTime).toLocaleString())
      console.log('权限过期时间:', new Date(unlimitedTime + 24 * 60 * 60 * 1000).toLocaleString())
    }

    return { unlimitedTime, hasAccess, remainingTime }
  }
}

// 创建全局分享管理器实例
const shareManager = new ShareManager()

export default shareManager
export { SHARE_CONFIG }
