/**
 * 首次使用管理器
 * 管理3个触发时机的逻辑
 */

const STORAGE_KEYS = {
  hasUsedBefore: 'hasUsedBefore',           // 是否使用过小程序
  hasShownAddGuide: 'hasShownAddGuide',     // 是否显示过添加引导
  hasShownTutorial: 'hasShownTutorial',     // 是否显示过教程
  lastSplashTime: 'lastSplashAdShowTime',   // 上次启动页时间
  lastEnterTime: 'lastEnterAdShowTime'      // 上次进入广告时间
}

class FirstUseManager {
  constructor() {
    this.config = {
      // 添加到常用小程序提示配置
      addTip: {
        enabled: true,
        showDuration: 10000,  // 显示10秒
        position: 'top-right' // 右上角
      }
    }
  }

  // 🔍 检查使用状态（简化版本，由App.vue统一管理）
  isFirstTimeEver() {
    // 这个方法现在只用于兼容，实际判断在App.vue中进行
    const hasUsedApp = uni.getStorageSync('hasUsedApp')
    return !hasUsedApp
  }

  isReenterApp() {
    // 这个方法现在只用于兼容，实际判断在App.vue中进行
    const hasUsedApp = uni.getStorageSync('hasUsedApp')
    return !!hasUsedApp
  }

  // 📝 标记使用状态（简化版本）
  markAsUsed() {
    uni.setStorageSync('hasUsedApp', true)
    uni.setStorageSync('firstUseTime', Date.now())
  }

  markAddGuideShown() {
    uni.setStorageSync(STORAGE_KEYS.hasShownAddGuide, true)
  }

  markTutorialShown() {
    uni.setStorageSync(STORAGE_KEYS.hasShownTutorial, true)
  }



  // 🔄 重置第一次使用状态（确保完整流程）
  resetFirstUseState() {
    console.log('[重置] 重置第一次使用状态')
    uni.removeStorageSync('hasUsedApp')
    uni.removeStorageSync('firstUseTime')
    uni.removeStorageSync(STORAGE_KEYS.hasShownAddGuide)
    uni.removeStorageSync(STORAGE_KEYS.hasShownTutorial)
    uni.removeStorageSync('lastSplashAdShowTime')
  }



  // 🎯 执行第一次使用流程
  async executeFirstTimeEverFlow() {
    console.log('[首次使用] 执行第一次使用流程')

    try {
      // 1. 显示添加常用小程序确认框（受 addTipConfig.globalEnabled 控制）
      const hasShownAddGuide = uni.getStorageSync(STORAGE_KEYS.hasShownAddGuide)
      console.log('[首次使用] hasShownAddGuide:', hasShownAddGuide)

      // 检查 addTipConfig 首次使用确认框开关
      const addTipManager = require('./add-tip-config.js').default
      const shouldShowAddGuide = addTipManager.shouldShowFirstUseDialog()

      if (!hasShownAddGuide && shouldShowAddGuide) {
        console.log('[首次使用] 显示添加常用小程序确认框')
        const addResult = await this.showAddToMiniProgramDialog()
        this.markAddGuideShown()

        if (addResult) {
          console.log('[首次使用] 用户确认了添加引导')
        }
      } else {
        console.log('[首次使用] 跳过添加引导（已显示过或全局禁用）')
        // 即使跳过，也要标记为已显示，避免后续重复检查
        if (!hasShownAddGuide) {
          this.markAddGuideShown()
        }
      }

      // 2. 显示教程推荐（教程提示框不受 addTipConfig 控制，第一次使用时必须显示）
      const hasShownTutorial = uni.getStorageSync(STORAGE_KEYS.hasShownTutorial)
      console.log('[首次使用] hasShownTutorial:', hasShownTutorial)

      if (!hasShownTutorial) {
        console.log('[首次使用] 显示教程推荐（第一次使用时必须显示）')
        const tutorialResult = await this.showTutorialDialog()
        this.markTutorialShown()

        if (tutorialResult) {
          console.log('[首次使用] 用户选择查看教程')
          uni.navigateTo({
            url: '/pages/tutorial/index'
          })
        }
      } else {
        console.log('[首次使用] 跳过教程推荐（已显示过）')
      }

      // 3. 标记为已使用
      this.markAsUsed()

    } catch (error) {
      console.error('[首次使用] 流程执行失败:', error)
    }
  }

  // 🎯 执行重新进入小程序流程
  async executeReenterAppFlow() {
    console.log('[重新进入] 执行重新进入小程序流程')

    try {
      // 检查 addTipConfig 悬浮提示开关后再显示浮动图标提示
      const addTipManager = require('./add-tip-config.js').default
      if (addTipManager.shouldShowFloatingTip()) {
        console.log('[重新进入] 显示浮动图标提示')
        this.showAddTipFloat()
      } else {
        console.log('[重新进入] addTipConfig 悬浮提示已禁用，跳过浮动提示')
      }

      // 不需要再次标记为已使用，因为用户已经使用过了

    } catch (error) {
      console.error('[重新进入] 流程执行失败:', error)
    }
  }

  // 💬 显示添加到常用小程序确认框（使用自定义弹窗）
  showAddToMiniProgramDialog() {
    return new Promise((resolve) => {
      // 触发显示自定义弹窗的事件
      uni.$emit('showFirstUseDialog', {
        title: '下次可以这样找到我',
        onConfirm: () => {
          resolve(true)
        }
      })
    })
  }

  // 📚 显示教程推荐弹窗
  showTutorialDialog() {
    return new Promise((resolve) => {
      uni.showModal({
        title: '新手教程',
        content: '这是您第一次使用，是否需要查看使用教程？\n教程将帮助您快速了解如何使用去水印功能。',
        confirmText: '查看教程',
        cancelText: '跳过',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  // 🏷️ 显示浮动图标提示
  showAddTipFloat() {
    console.log('[浮动提示] 显示添加到常用小程序提示')
    
    // 触发显示浮动提示事件
    uni.$emit('showAddTip', {
      duration: this.config.addTip.showDuration,
      position: this.config.addTip.position
    })
  }

  // 🎯 根据场景执行对应流程
  async executeByScene(scene) {
    console.log('[场景执行] 场景:', scene)

    switch (scene) {
      case 'firstTimeEver':
        await this.executeFirstTimeEverFlow()
        break

      case 'reenterApp':
        await this.executeReenterAppFlow()
        break

      case 'appResume':
        // 切换应用场景，只显示弹窗广告，由广告管理器处理
        console.log('[场景执行] 切换应用场景，由广告管理器处理')
        break

      default:
        console.log('[场景执行] 未知场景:', scene)
    }
  }

  // 🔧 获取当前应该执行的场景
  getCurrentScene() {
    const isFirstEver = this.isFirstTimeEver()
    const isReenter = this.isReenterApp()

    console.log('[场景判断] isFirstTimeEver:', isFirstEver)
    console.log('[场景判断] isReenterApp:', isReenter)

    if (isFirstEver) {
      console.log('[场景判断] 返回场景: firstTimeEver')
      return 'firstTimeEver'
    } else if (isReenter) {
      console.log('[场景判断] 返回场景: reenterApp')
      return 'reenterApp'
    } else {
      console.log('[场景判断] 返回场景: normal')
      return 'normal'
    }
  }
}

// 导出单例
const firstUseManager = new FirstUseManager()
export default firstUseManager
