<template>
  <view class="image-selector-mask" v-if="show" @click="onMaskClick">
    <view class="image-selector-container" @click.stop>
      <!-- 标题栏 -->
      <view class="selector-header">
        <text class="selector-title">选择要保存的图片</text>
        <text class="selector-subtitle">共{{ imageList.length }}张图片</text>
      </view>

      <!-- 操作按钮 -->
      <view class="selector-actions">
        <view class="left-actions">
          <button class="action-btn" :class="{ 'active': isAllSelected }" @click="toggleSelectAll">
            {{ isAllSelected ? '取消全选' : '全选' }}
          </button>
        </view>
        <text class="selected-count">已选择 {{ selectedCount }} 张</text>
      </view>

      <!-- 图片网格 - 使用最简单的scroll-view -->
      <scroll-view 
        class="image-grid" 
        scroll-y
        style="height: 400px;"
      >
        <view class="grid-container">
          <view 
            class="image-item" 
            v-for="(item, index) in imageList" 
            :key="index"
            @click="toggleSelect(index)"
          >
            <image 
              :src="item.url" 
              mode="aspectFill" 
              class="preview-image"
            />
            <view class="image-overlay" :class="{ 'selected': item.selected }">
              <view class="checkbox" :class="{ 'checked': item.selected }">
                <text class="checkbox-icon" v-if="item.selected">✓</text>
              </view>
              <text class="image-number">{{ index + 1 }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="selector-footer">
        <button class="footer-btn cancel-btn" @click="onCancel">取消</button>
        <button 
          class="footer-btn confirm-btn" 
          :class="{ 'disabled': selectedCount === 0 }"
          :disabled="selectedCount === 0"
          @click="onConfirm"
        >
          保存选中的图片 ({{ selectedCount }})
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImageSelectorSimple',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      imageList: []
    }
  },
  
  computed: {
    selectedCount() {
      return this.imageList.filter(item => item.selected).length
    },
    
    isAllSelected() {
      return this.imageList.length > 0 && this.selectedCount === this.imageList.length
    }
  },
  
  watch: {
    images: {
      handler(newImages) {
        this.imageList = newImages.map((url, index) => ({
          url: url,
          index: index,
          selected: true, // 默认全选
          error: false
        }))
      },
      immediate: true
    }
  },
  
  methods: {
    // 切换单个图片选择状态
    toggleSelect(index) {
      this.imageList[index].selected = !this.imageList[index].selected

      // 添加触觉反馈
      uni.vibrateShort({
        type: 'light'
      })
    },

    // 切换全选状态
    toggleSelectAll() {
      const newState = !this.isAllSelected
      this.imageList.forEach(item => {
        item.selected = newState
      })

      // 添加触觉反馈
      uni.vibrateShort({
        type: 'light'
      })
    },
    
    // 点击遮罩
    onMaskClick() {
      this.onCancel()
    },
    
    // 取消选择
    onCancel() {
      this.$emit('cancel')
    },
    
    // 确认选择
    onConfirm() {
      const selectedIndexes = this.imageList
        .filter(item => item.selected)
        .map(item => item.index)
      
      this.$emit('confirm', selectedIndexes)
    }
  }
}
</script>

<style scoped>
.image-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.image-selector-container {
  background: #ffffff;
  border-radius: 12px;
  width: 100%;
  max-width: 340px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.selector-header {
  padding: 20px 15px 10px;
  text-align: center;
  border-bottom: 1px solid #E5E5E5;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.selector-subtitle {
  font-size: 13px;
  color: #666;
}

.selector-actions {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E5E5E5;
}

.left-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 2rpx solid #007AFF;
  background: #F0F8FF;
  color: #007AFF;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.active {
  background: #007AFF;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

.selected-count {
  font-size: 13px;
  color: #666;
}

.image-grid {
  background: #f8f8f8;
}

.grid-container {
  padding: 10px 15px 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.image-item {
  width: 100%;
  aspect-ratio: 1;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
  background: #F5F5F5;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 8px;
  transition: all 0.3s ease;
}

.image-overlay.selected {
  background: rgba(0, 122, 255, 0.4);
  border: 2px solid #007AFF;
  padding: 6px;
}

.checkbox {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 2px solid #ffffff;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007AFF;
  border-color: #ffffff;
}

.checkbox-icon {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}

.image-number {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 8px;
  min-width: 20px;
  text-align: center;
}

.selector-footer {
  padding: 30rpx;
  display: flex;
  gap: 24rpx;
  border-top: 2rpx solid #E5E5E5;
  background: #F8F9FA;
}

.footer-btn {
  height: 96rpx;
  border-radius: 48rpx;
  border: none !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  /* 重置小程序button默认样式 */
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal !important;
  text-align: center !important;
}

.footer-btn.cancel-btn {
  flex: 0 0 160rpx;
  background: #F8F9FA !important;
  color: #666 !important;
  border: 2rpx solid #E5E5E5 !important;
}

.footer-btn.cancel-btn:active {
  background: #E9ECEF !important;
}

.footer-btn.confirm-btn {
  flex: 1;
  background: linear-gradient(45deg, #007AFF, #5AC8FA) !important;
  color: #ffffff !important;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3) !important;
}

.footer-btn.confirm-btn:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3) !important;
}

.footer-btn.confirm-btn.disabled {
  background: #CCCCCC !important;
  color: #999999 !important;
  box-shadow: none !important;
  transform: none !important;
}
</style>
