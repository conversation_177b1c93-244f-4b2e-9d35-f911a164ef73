<template>
  <view class="image-selector-mask" v-if="show" @click="onMaskClick">
    <view class="image-selector-container" @click.stop>
      <!-- 标题栏 -->
      <view class="selector-header">
        <text class="selector-title">选择要保存的图片</text>
        <text class="selector-subtitle">共{{ imageList.length }}张图片</text>
      </view>

      <!-- 操作按钮 -->
      <view class="selector-actions">
        <view class="left-actions">
          <button class="action-btn" :class="{ 'active': isAllSelected }" @click="toggleSelectAll">
            {{ isAllSelected ? '取消全选' : '全选' }}
          </button>
        </view>
        <text class="selected-count">已选择 {{ selectedCount }} 张</text>
      </view>

      <!-- 图片网格 -->
      <scroll-view
        class="image-grid"
        scroll-y="true"
        :style="{ height: scrollViewHeight + 'px' }"
        :scroll-top="scrollTop"
        :enable-back-to-top="false"
        :scroll-with-animation="false"
        @scroll="onScroll"
        @scrolltoupper="onScrollToUpper"
        @scrolltolower="onScrollToLower"
      >
        <view class="grid-container">
          <view
            class="image-item"
            v-for="(item, index) in imageList"
            :key="index"
            @click="toggleSelect(index)"
          >
            <image
              :src="item.url"
              mode="aspectFill"
              class="preview-image"
              @error="onImageError(index)"
            />
            <view class="image-overlay" :class="{ 'selected': item.selected }">
              <view class="checkbox" :class="{ 'checked': item.selected }">
                <text class="checkbox-icon" v-if="item.selected">✓</text>
              </view>
              <text class="image-number">{{ index + 1 }}</text>
            </view>
            <view class="loading-error" v-if="item.error">
              <text class="error-text">加载失败</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="selector-footer">
        <button class="footer-btn cancel-btn" @click="onCancel">取消</button>
        <button 
          class="footer-btn confirm-btn" 
          :class="{ 'disabled': selectedCount === 0 }"
          :disabled="selectedCount === 0"
          @click="onConfirm"
        >
          保存选中的图片 ({{ selectedCount }})
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImageSelector',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      imageList: [],
      scrollTop: 0,
      scrollViewHeight: 400
    }
  },
  
  computed: {
    selectedCount() {
      return this.imageList.filter(item => item.selected).length
    },
    
    isAllSelected() {
      return this.imageList.length > 0 && this.selectedCount === this.imageList.length
    }
  },
  
  watch: {
    images: {
      handler(newImages) {
        this.imageList = newImages.map((url, index) => ({
          url: url,
          index: index,
          selected: true, // 默认全选
          error: false
        }))
      },
      immediate: true
    }
  },
  
  methods: {
    // 切换单个图片选择状态
    toggleSelect(index) {
      this.imageList[index].selected = !this.imageList[index].selected
    },
    
    // 切换全选状态
    toggleSelectAll() {
      const newState = !this.isAllSelected
      this.imageList.forEach(item => {
        item.selected = newState
      })
    },
    
    // 图片加载错误
    onImageError(index) {
      this.imageList[index].error = true
    },

    // 滚动事件
    onScroll(e) {
      console.log('滚动事件:', e.detail)
      this.scrollTop = e.detail.scrollTop
    },

    // 滚动到顶部
    onScrollToUpper() {
      console.log('滚动到顶部')
    },

    // 滚动到底部
    onScrollToLower() {
      console.log('滚动到底部')
    },

    // 点击遮罩
    onMaskClick() {
      this.onCancel()
    },

    // 取消选择
    onCancel() {
      this.$emit('cancel')
    },

    // 确认选择
    onConfirm() {
      const selectedIndexes = this.imageList
        .filter(item => item.selected)
        .map(item => item.index)

      this.$emit('confirm', selectedIndexes)
    }
  },

  mounted() {
    this.calculateScrollViewHeight()
  },

  methods: {
    // 计算scroll-view高度
    calculateScrollViewHeight() {
      uni.getSystemInfo({
        success: (res) => {
          console.log('系统信息:', res)
          // 计算可用高度：屏幕高度 - 状态栏 - 标题栏 - 操作栏 - 底部按钮 - 边距
          const availableHeight = res.windowHeight
          const headerHeight = 60 // 标题栏高度
          const actionsHeight = 60 // 操作栏高度
          const footerHeight = 120 // 底部按钮高度
          const padding = 80 // 上下边距

          this.scrollViewHeight = availableHeight - headerHeight - actionsHeight - footerHeight - padding
          console.log('计算的scroll-view高度:', this.scrollViewHeight)
        }
      })
    }
  }
}
</script>

<style scoped>
.image-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  overflow: hidden;
  touch-action: none;
}

.image-selector-container {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 680rpx;
  height: 85vh;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.selector-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #E5E5E5;
}

.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.selector-subtitle {
  font-size: 26rpx;
  color: #666;
}

.selector-actions {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5E5;
}

.left-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #007AFF;
  background: #ffffff;
  color: #007AFF;
  font-size: 26rpx;
  font-weight: 500;
}

.action-btn.active {
  background: #007AFF;
  color: #ffffff;
}

.selected-count {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.image-grid {
  height: 600rpx;
  width: 100%;
  background: #f8f8f8;
}

.grid-container {
  padding: 20rpx 30rpx 40rpx 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  min-height: 100%;
}

.image-item {
  width: 100%;
  aspect-ratio: 1;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.image-item:active {
  transform: scale(0.95);
}

.preview-image {
  width: 100%;
  height: 100%;
  background: #F5F5F5;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.image-overlay.selected {
  background: rgba(0, 122, 255, 0.4);
  border: 4rpx solid #007AFF;
  padding: 12rpx;
}

.checkbox {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  border: 3rpx solid #ffffff;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.checkbox.checked {
  background: #007AFF;
  border-color: #ffffff;
  transform: scale(1.1);
}

.checkbox-icon {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

.image-number {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  min-width: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.loading-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-text {
  color: #ffffff;
  font-size: 24rpx;
}

.selector-footer {
  padding: 24rpx 30rpx 30rpx;
  display: flex;
  gap: 24rpx;
  border-top: 1rpx solid #E5E5E5;
  background: #FAFAFA;
}

.footer-btn {
  height: 96rpx;
  border-radius: 48rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn {
  flex: 0 0 160rpx;
  background: #F8F9FA;
  color: #666;
  border: 2rpx solid #E5E5E5;
}

.cancel-btn:active {
  background: #E9ECEF;
}

.confirm-btn {
  flex: 1;
  background: linear-gradient(45deg, #007AFF, #5AC8FA);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
}

.confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.confirm-btn.disabled {
  background: #CCCCCC;
  color: #999999;
  box-shadow: none;
  transform: none;
}
</style>
