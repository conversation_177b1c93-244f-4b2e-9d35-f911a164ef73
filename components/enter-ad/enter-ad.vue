<template>
  <view v-if="showAd" class="enter-ad-overlay">
    <view class="enter-ad-container">
      <view class="ad-header">
        <text class="ad-title">📺 进入小程序广告</text>
        <text class="close-btn" @click="closeAd">❌</text>
      </view>
      
      <view class="ad-content">
        <view class="video-placeholder">
          <text class="play-icon">▶️</text>
          <text class="ad-text">视频广告播放中...</text>
          <text class="ad-subtitle">感谢您的支持</text>
        </view>
      </view>
      
      <view class="ad-footer">
        <!-- 移除继续使用按钮，广告会自动关闭 -->
      </view>
    </view>
  </view>
</template>

<script>
import adManager from '../ad-config.js'

export default {
  name: 'EnterAd',
  data() {
    return {
      showAd: false,
      countdown: 5,
      countdownTimer: null,
      eventListenerAdded: false,
      hasShownAd: false
    }
  },
  
  mounted() {
    // 监听进入广告事件（确保只添加一次）
    if (!this.eventListenerAdded) {
      uni.$on('showEnterAd', this.showEnterAd)
      this.eventListenerAdded = true
    }
  },
  
  beforeDestroy() {
    // 组件销毁前清理
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
    
    // 移除事件监听
    if (this.eventListenerAdded) {
      uni.$off('showEnterAd', this.showEnterAd)
      this.eventListenerAdded = false
    }
  },
  
  onUnload() {
    // 页面卸载时也清理
    this.beforeDestroy()
  },
  
  methods: {
    
    // 显示进入广告
    showEnterAd() {
      // 防止重复显示（同时只能有一个广告）
      if (this.showAd) return
      
      const config = adManager.getAdConfig('enter')
      this.countdown = config.duration || 5
      this.showAd = true
      
      console.log('[进入广告] 开始显示广告，倒计时:', this.countdown)
      
      // 开始倒计时
      this.startCountdown()
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdownTimer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          // 倒计时结束，自动关闭广告
          this.autoCloseAd()
        }
      }, 1000)
    },
    
    // 自动关闭广告
    autoCloseAd() {
      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      this.$emit('complete', { watched: true, autoClosed: true })
    },
    
    // 用户主动关闭广告
    closeAd() {
      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      this.$emit('complete', { closed: true })
    }
  }
}
</script>

<style scoped>
.enter-ad-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.enter-ad-container {
  background: #FFFFFF;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
  background: #F8F9FA;
}

.close-btn {
  font-size: 32rpx;
  color: #999;
  cursor: pointer;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

.ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.countdown {
  font-size: 24rpx;
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.ad-content {
  padding: 40rpx;
}

.video-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  min-height: 200rpx;
  justify-content: center;
}

.play-icon {
  font-size: 60rpx;
  margin-bottom: 12rpx;
}

.ad-text {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.ad-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.ad-footer {
  padding: 20rpx 30rpx 30rpx;
}

/* 移除继续使用按钮样式，因为按钮已被删除 */
</style>
