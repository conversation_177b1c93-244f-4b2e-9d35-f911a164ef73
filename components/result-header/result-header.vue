<template>
  <view class="header" :style="{ paddingTop: statusBarHeight + 'px', height: customNavHeight + 'px' }">
    <view class="header-content">
      <view class="back-btn" @click="$emit('back')">
        <view class="back-btn-inner">
          <text class="back-icon">‹</text>
          <text class="back-text">返回</text>
        </view>
      </view>
      <text class="title">{{ title }}</text>
      <view class="placeholder"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ResultHeader',
  props: {
    title: {
      type: String,
      default: '处理结果'
    },
    statusBarHeight: {
      type: Number,
      default: 0
    },
    customNavHeight: {
      type: Number,
      default: 88
    }
  },
  emits: ['back']
}
</script>

<style scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 120rpx;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.back-btn-inner {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #FFFFFF;
  margin-right: 8rpx;
  font-weight: 600;
  line-height: 1;
}

.back-text {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
  text-align: center;
  flex: 1;
}

.placeholder {
  width: 120rpx;
  display: flex;
  justify-content: flex-end;
}
</style>