<template>
  <view class="video-info">
    <text class="video-title">{{ title }}</text>
    <text v-if="content && content.trim() && type !== 'text'" class="video-content">{{ content }}</text>
    <text class="video-author">作者：{{ author }}</text>
    <text v-if="shouldShowSource" class="video-source">来源：{{ displaySource }}</text>
  </view>
</template>

<script>
import adManager from '../ad-config.js'

export default {
  name: 'ResultVideoInfo',
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    author: {
      type: String,
      default: ''
    },
    source: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    platform: {
      type: String,
      default: ''
    }
  },
  computed: {
    shouldShowSource() {
      return adManager.config.showPlatformSource
    },
    displaySource() {
      if (!this.shouldShowSource) {
        return '短视频平台'
      }
      return adManager.getPlatformDisplayName(this.platform) || this.source || '短视频平台'
    }
  }
}
</script>

<style scoped>
.video-info {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.video-content {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
  white-space: pre-wrap; /* 保持换行格式 */
  word-break: break-all;
}

.video-author,
.video-source {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}
</style>