<template>
  <view v-if="show" class="live-photo-selector-mask" @click="cancel">
    <view class="live-photo-selector-container" @click.stop>
      <view class="selector-header">
        <text class="selector-title">选择要保存的 Live Photo</text>
        <view class="selector-close" @click="cancel">✕</view>
      </view>
      
      <view class="selector-content">
        <text class="selector-subtitle">共 {{ livePhotoItems.length }} 个 Live Photo，请选择要保存的项目</text>
        
        <scroll-view class="video-list" scroll-y>
          <view class="video-item"
                v-for="(item, listIndex) in livePhotoItems"
                :key="item.index"
                :class="{ 'selected': selectedIndexes.includes(item.index) }"
                @click="toggleSelection(item.index)">
            <view class="video-preview">
              <video
                :src="item.videoUrl"
                class="preview-video"
                :poster="item.imageUrl"
                :controls="false"
                :autoplay="false"
                :show-center-play-btn="false"
              ></video>
              <view class="video-overlay">
                <view class="play-icon">▶</view>
              </view>
            </view>

            <view class="video-info">
              <text class="video-title">Live Photo {{ item.index + 1 }}</text>
              <text class="video-size">视频文件</text>
            </view>

            <view class="video-checkbox">
              <view class="checkbox" :class="{ 'checked': selectedIndexes.includes(item.index) }">
                <text v-if="selectedIndexes.includes(item.index)" class="check-mark">✓</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <view class="selector-footer">
        <view class="selection-info">
          <text class="selection-count">已选择 {{ selectedIndexes.length }} 个</text>
        </view>
        
        <view class="action-buttons">
          <button class="action-btn cancel-btn" @click="cancel">取消</button>
          <button class="action-btn select-all-btn" @click="selectAll">
            {{ selectedIndexes.length === videoUrls.length ? '全不选' : '全选' }}
          </button>
          <button class="action-btn confirm-btn" @click="confirm" :disabled="selectedIndexes.length === 0">
            {{ selectedIndexes.length === videoUrls.length ? '保存全部' : `保存 (${selectedIndexes.length})` }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LivePhotoSelector',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    videoUrls: {
      type: Array,
      default: () => []
    },
    imageUrls: {
      type: Array,
      default: () => []
    },
    livePhotoVideos: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      selectedIndexes: []
    }
  },
  
  computed: {
    // 获取有Live Photo的图片列表
    livePhotoItems() {
      const items = [];
      for (let i = 0; i < this.imageUrls.length; i++) {
        const livePhotoUrl = this.livePhotoVideos[i];
        if (livePhotoUrl) {
          items.push({
            index: i,
            imageUrl: this.imageUrls[i],
            videoUrl: livePhotoUrl
          });
        }
      }
      return items;
    }
  },
  
  watch: {
    show(newVal) {
      console.log('Live Photo 选择器显示状态变化:', newVal)
      if (newVal) {
        console.log('Live Photo 选择器显示，Live Photo数量:', this.livePhotoItems.length)
        console.log('Live Photo 选择器显示，图片数量:', this.imageUrls.length)
        // 重置选择状态
        this.selectedIndexes = []
      }
    }
  },
  
  methods: {
    toggleSelection(index) {
      const selectedIndex = this.selectedIndexes.indexOf(index)
      if (selectedIndex > -1) {
        this.selectedIndexes.splice(selectedIndex, 1)
      } else {
        this.selectedIndexes.push(index)
      }

      // 添加触觉反馈
      uni.vibrateShort({
        type: 'light'
      })
    },
    
    selectAll() {
      if (this.selectedIndexes.length === this.livePhotoItems.length) {
        // 全部取消选择
        this.selectedIndexes = []
      } else {
        // 全部选择
        this.selectedIndexes = this.livePhotoItems.map(item => item.index)
      }
    },
    
    confirm() {
      if (this.selectedIndexes.length === 0) {
        uni.showToast({
          title: '请至少选择一个Live Photo',
          icon: 'none'
        })
        return
      }
      
      this.$emit('confirm', this.selectedIndexes)
    },
    
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.live-photo-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.live-photo-selector-container {
  width: 100%;
  max-width: 700rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E5E5E5;
}

.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.selector-close {
  width: 60rpx;
  height: 60rpx;
  background: #E5E5E5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
}

.selector-content {
  flex: 1;
  padding: 30rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.video-list {
  flex: 1;
  max-height: 400rpx;
}

.video-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
  border-radius: 12rpx;
  margin-bottom: 10rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-item:hover {
  background: #F8F9FA;
}

.video-item.selected {
  background: #E3F2FD;
  border: 2rpx solid #007AFF;
}

.video-preview {
  position: relative;
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #F5F5F5;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  color: #ffffff;
  font-size: 24rpx;
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-right: 20rpx;
}

.video-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.video-size {
  font-size: 24rpx;
  color: #999;
}

.video-checkbox {
  flex-shrink: 0;
}

.checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007AFF;
  border-color: #007AFF;
  transform: scale(1.1);
}

.check-mark {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: bold;
}

.selector-footer {
  padding: 30rpx;
  background: #F8F9FA;
  border-top: 1rpx solid #E5E5E5;
}

.selection-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.selection-count {
  font-size: 26rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  height: 96rpx;
  border-radius: 48rpx;
  border: none !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  transition: all 0.3s ease;
  /* 重置小程序button默认样式 */
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal !important;
  text-align: center !important;
}

.action-btn.cancel-btn {
  flex: 0 0 160rpx;
  background: #F8F9FA !important;
  color: #666 !important;
  border: 2rpx solid #E5E5E5 !important;
}

.action-btn.cancel-btn:active {
  background: #E9ECEF !important;
}

.action-btn.select-all-btn {
  flex: 0 0 160rpx;
  background: #F0F8FF !important;
  color: #007AFF !important;
  border: 2rpx solid #007AFF !important;
}

.action-btn.select-all-btn:active {
  background: #E3F2FD !important;
}

.action-btn.confirm-btn {
  flex: 1;
  min-width: 240rpx;
  background: linear-gradient(45deg, #007AFF, #5AC8FA) !important;
  color: #ffffff !important;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3) !important;
}

.action-btn.confirm-btn:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3) !important;
}

.confirm-btn:disabled {
  background: #CCCCCC !important;
  color: #999999 !important;
  box-shadow: none !important;
  transform: none !important;
}
</style>
