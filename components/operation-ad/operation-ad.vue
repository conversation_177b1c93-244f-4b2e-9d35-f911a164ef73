<template>
  <view v-if="showAd" class="operation-ad-overlay">
    <view class="operation-ad-container" @click.stop="">
      <view class="ad-header">
        <view class="placeholder"></view>
        <text class="close-btn" @click.stop="closeAd">✕</text>
      </view>
      
      <view class="ad-content">
        <view class="ad-video-placeholder">
          <text class="play-icon">{{ contentIcon }}</text>
          <text class="ad-text">获取24小时解析权限</text>
          <text class="ad-subtitle">{{ contentSubtitle }}</text>
        </view>
      </view>

      <view class="ad-actions">
        <!-- 主要操作按钮（横向排列） -->
        <view class="main-actions">
          <!-- 观看广告按钮 -->
          <button v-if="showWatchAdButton" class="action-btn watch-btn" @click="watchAd">
            <text class="action-text">📺 观看广告</text>
          </button>

          <!-- 分享好友按钮 -->
          <button v-if="showShareButton" class="action-btn share-btn" @click="shareToFriend">
            <text class="action-text">📤 分享好友</text>
          </button>
        </view>

        <!-- 下次再说按钮 -->
        <button class="continue-btn" @click="continueWithoutAd">
          <text class="continue-text">下次再说</text>
        </button>
      </view>
    </view>
  </view>
  
  <!-- 模拟微信激励视频广告 -->
  <view v-if="showFullscreenAdModal" class="mock-ad-overlay">
    <!-- 模拟官方广告顶部栏 -->
    <view class="mock-ad-topbar">
      <view class="mock-ad-left">
        <text v-if="fullscreenAdCountdown <= 0" class="mock-ad-close" @click="closeFullscreenAd">关闭</text>
      </view>
      <view class="mock-ad-info">
        <text class="mock-ad-label">广告</text>
        <text class="mock-ad-countdown">{{ fullscreenAdCountdown }}秒后可获得奖励</text>
      </view>
      <view class="mock-ad-right">
        <text class="mock-ad-sound">🔊</text>
      </view>
    </view>
    
    <!-- 模拟广告内容区域 -->
    <view class="mock-ad-content">
      <view class="mock-ad-video">
        <text class="mock-ad-play">▶️</text>
        <text class="mock-ad-title">模拟广告内容</text>
        <text class="mock-ad-desc">这是一个模拟的激励视频广告</text>
      </view>
      
      <!-- 模拟广告下方的推广内容 -->
      <view v-if="fullscreenAdCountdown <= 0" class="mock-ad-promotion">
        <view class="mock-ad-app">
          <image class="mock-ad-icon" src="/static/logo.png" mode="aspectFit"></image>
          <view class="mock-ad-details">
            <text class="mock-ad-app-name">MarkEraser</text>
            <text class="mock-ad-app-desc">短视频去水印工具</text>
            <view class="mock-ad-rating">
              <text class="mock-ad-stars">⭐⭐⭐⭐⭐</text>
              <text class="mock-ad-score">4.8分</text>
            </view>
          </view>
          <view class="mock-ad-button" @click="closeFullscreenAd">
            <text class="mock-ad-button-text">立即体验</text>
          </view>
        </view>
        <text class="mock-ad-note">进入后，广告继续计时</text>
      </view>
    </view>
  </view>
</template>

<script>
import adManager from '../ad-config.js'
import shareManager from '../share-config.js'

export default {
  name: 'OperationAd',
  data() {
    return {
      showAd: false,
      countdown: 3,
      countdownTimer: null,
      actionType: '',
      callback: null,
      // 模拟广告相关
      showFullscreenAdModal: false,
      fullscreenAdCountdown: 5,
      fullscreenAdTimer: null,
      // 防止重复关闭
      isClosing: false,
      // 按钮显示配置（用于强制分享时覆盖默认逻辑）
      buttonConfig: null
    }
  },
  
  computed: {
    actionTypeText() {
      const actionMap = {
        'saveVideo': '保存视频前的广告',
        'saveCover': '保存封面前的广告',
        'saveToAlbum': '保存到相册前的广告',
        'copyLink': '复制链接前的广告',
        'copyText': '复制文案前的广告'
      }
      return actionMap[this.actionType] || '操作前广告'
    },

    // 是否显示观看广告按钮
    showWatchAdButton() {
      // 如果有按钮配置，优先使用配置
      if (this.buttonConfig && this.buttonConfig.hasOwnProperty('showWatchAdButton')) {
        return this.buttonConfig.showWatchAdButton
      }
      // 否则使用默认逻辑
      return adManager.shouldShowAd('operation', this.actionType)
    },

    // 是否显示分享按钮
    showShareButton() {
      // 如果有按钮配置，优先使用配置
      if (this.buttonConfig && this.buttonConfig.hasOwnProperty('showShareButton')) {
        return this.buttonConfig.showShareButton
      }
      // 否则使用默认逻辑
      return shareManager.shouldShowShare('resultPage')
    },

    // 根据按钮组合显示不同的图标
    contentIcon() {
      const hasWatchAd = this.showWatchAdButton
      const hasShare = this.showShareButton

      if (hasWatchAd && hasShare) {
        // 两个按钮都有：使用礼品盒图标
        return '🎁'
      } else if (hasWatchAd && !hasShare) {
        // 只有观看广告：使用视频图标
        return '📺'
      } else if (!hasWatchAd && hasShare) {
        // 只有分享：使用分享图标
        return '📤'
      } else {
        // 都没有（异常情况）：使用默认图标
        return '🎯'
      }
    },

    // 根据按钮组合显示不同的说明文字
    contentSubtitle() {
      const hasWatchAd = this.showWatchAdButton
      const hasShare = this.showShareButton

      if (hasWatchAd && hasShare) {
        // 两个按钮都有
        return '观看广告或分享好友，即可获得24小时无限解析权限'
      } else if (hasWatchAd && !hasShare) {
        // 只有观看广告
        return '观看广告，即可获得24小时无限解析权限'
      } else if (!hasWatchAd && hasShare) {
        // 只有分享
        return '分享给好友，即可获得24小时无限解析权限'
      } else {
        // 都没有（异常情况）
        return '完成操作，即可获得24小时无限解析权限'
      }
    }
  },
  
  mounted() {
    // 监听显示操作广告事件
    uni.$on('showOperationAd', this.handleShowAd)
  },
  
  beforeDestroy() {
    uni.$off('showOperationAd', this.handleShowAd)
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  
  methods: {
    // 处理显示广告事件
    handleShowAd(options) {
      const { config, actionType, callback } = options
      this.actionType = actionType
      this.callback = callback
      this.buttonConfig = config // 保存按钮配置
      this.showAd = true
      this.isClosing = false // 重置关闭状态
    },
    
    // 关闭广告
    closeAd() {
      // 防止重复关闭
      if (this.isClosing) return
      this.isClosing = true
      
      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      
      // 只调用一次回调
      if (this.callback) {
        this.callback(false) // 取消操作
        this.callback = null // 置空防止重复调用
      }
      
      // 触发完成事件
      this.$emit('complete', { closed: true, cancelled: true })
    },
    
    // 观看广告
    watchAd() {
      // 隐藏当前广告弹窗
      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      
      // 显示模拟广告
      this.showMockAd()
    },
    
    // 显示模拟广告
    showMockAd() {
      this.showFullscreenAdModal = true
      
      // 从广告管理器获取配置的时长
      const config = adManager.getAdConfig('operation')
      this.fullscreenAdCountdown = config.fullscreenAdDuration || 5
      
      // 开始倒计时
      this.startMockAdCountdown()
    },
    
    // 开始模拟广告倒计时
    startMockAdCountdown() {
      this.fullscreenAdTimer = setInterval(() => {
        if (this.fullscreenAdCountdown > 0) {
          this.fullscreenAdCountdown--
        } else {
          // 倒计时结束，可以关闭
          clearInterval(this.fullscreenAdTimer)
        }
      }, 1000)
    },
    
    // 关闭模拟广告（看完获得奖励）
    closeFullscreenAd() {
      // 防止重复关闭
      if (this.isClosing) return
      this.isClosing = true
      
      this.showFullscreenAdModal = false
      this.showAd = false
      
      if (this.fullscreenAdTimer) {
        clearInterval(this.fullscreenAdTimer)
      }
      
      // 给用户24小时解析权限
      adManager.setUnlimitedAccess()
      
      // 只调用一次回调
      if (this.callback) {
        this.callback(true)
        this.callback = null
      }
      
      // 触发完成事件
      this.$emit('complete', { watched: true, rewarded: true })
    },
    

    
    // 分享给好友
    shareToFriend() {
      console.log('[操作广告] 用户选择分享好友')

      // 隐藏当前广告弹窗
      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }

      // 显示分享弹窗
      shareManager.showShareDialog({
        entryType: 'resultPage',
        showRewardHint: true,
        callback: (shareResult) => {
          if (shareResult.success) {
            console.log('[操作广告] 分享成功，允许操作')
            // 分享成功，权限已在 shareManager.handleShareSuccess 中设置

            // 分享成功，允许操作
            if (this.callback) {
              this.callback(true)
              this.callback = null
            }
            this.$emit('complete', { shared: true, rewarded: true })
          } else {
            console.log('[操作广告] 分享失败或取消')
            // 分享失败，重新显示广告弹窗
            this.showAd = true
          }
        }
      })
    },

    // 暂不观看广告，直接关闭弹窗
    continueWithoutAd() {
      // 防止重复关闭
      if (this.isClosing) return
      this.isClosing = true

      this.showAd = false
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }

      // 只调用一次回调
      if (this.callback) {
        this.callback(false) // 取消操作
        this.callback = null // 置空防止重复调用
      }

      // 触发完成事件
      this.$emit('complete', { watched: false, cancelled: true })
    }
  }
}
</script>

<style scoped>
.operation-ad-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.operation-ad-container {
  background: #FFFFFF;
  border-radius: 20rpx;
  width: 100%;
  max-width: 500rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.2);
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
  background: #F8F9FA;
}

.ad-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.countdown {
  font-size: 22rpx;
  color: #FFFFFF;
  background: #FF6B35;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.close-btn {
  font-size: 24rpx;
  color: #666;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 25rpx;
  background: #F0F0F0;
}

.close-btn:active {
  background: #E0E0E0;
}

.ad-content {
  padding: 30rpx;
}

.ad-video-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  padding: 50rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  min-height: 150rpx;
  justify-content: center;
}

.play-icon {
  font-size: 50rpx;
  margin-bottom: 8rpx;
}

.ad-text {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.ad-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.ad-actions {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.main-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watch-btn {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  color: #FFFFFF;
}

.share-btn {
  background: linear-gradient(45deg, #007AFF, #5856D6);
  color: #FFFFFF;
}

.continue-btn {
  width: 100%;
  height: 60rpx;
  background: #F5F5F5;
  color: #666;
  border: none;
  border-radius: 30rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:active {
  transform: scale(0.98);
}

.continue-btn:active {
  background: #E8E8E8;
}

.action-text,
.continue-text {
  font-size: inherit;
}

.placeholder {
  flex: 1;
}

/* 模拟微信激励视频广告样式 */
.mock-ad-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  z-index: 10000;
  display: flex;
  flex-direction: column;
}

.mock-ad-topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 100rpx 30rpx 20rpx 30rpx; /* 增加顶部padding，避开微信状态栏 */
  background: rgba(0, 0, 0, 0.7);
}

.mock-ad-left {
  min-width: 120rpx; /* 为关闭按钮预留空间 */
}

.mock-ad-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  justify-content: center; /* 居中显示广告信息 */
}

.mock-ad-right {
  min-width: 120rpx; /* 为右侧内容预留空间 */
  display: flex;
  justify-content: flex-end;
}

.mock-ad-label {
  font-size: 24rpx;
  color: #FFFFFF;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.mock-ad-countdown {
  font-size: 26rpx;
  color: #FFFFFF;
}

/* 移除旧的 mock-ad-controls 样式，已重构为 mock-ad-left 和 mock-ad-right */

.mock-ad-sound {
  font-size: 32rpx;
}

.mock-ad-close {
  font-size: 28rpx;
  color: #FFFFFF;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.mock-ad-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 40rpx;
}

.mock-ad-video {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 80rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  min-width: 400rpx;
  min-height: 300rpx;
  justify-content: center;
  margin-bottom: 60rpx;
}

.mock-ad-play {
  font-size: 80rpx;
  color: #FFFFFF;
}

.mock-ad-title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.mock-ad-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.mock-ad-promotion {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  width: 100%;
  max-width: 600rpx;
}

.mock-ad-app {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.mock-ad-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
}

.mock-ad-details {
  flex: 1;
}

.mock-ad-app-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  display: block;
}

.mock-ad-app-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 8rpx;
}

.mock-ad-rating {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 8rpx;
}

.mock-ad-stars {
  font-size: 20rpx;
}

.mock-ad-score {
  font-size: 22rpx;
  color: #007AFF;
}

.mock-ad-button {
  background: #00C851;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  min-width: 120rpx;
  text-align: center;
}

.mock-ad-button-text {
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.mock-ad-note {
  font-size: 22rpx;
  color: #999;
  text-align: center;
}

.mock-ad-close:active,
.mock-ad-button:active {
  opacity: 0.8;
}
</style>
