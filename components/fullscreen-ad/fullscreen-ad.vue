<template>
  <view v-if="visible" class="fullscreen-ad-mask">
    <!-- 顶部状态栏 -->
    <view class="ad-status-bar">
      <text class="ad-label">📺 广告</text>
      <view class="skip-button" v-if="canClose" @click="closeAd">
        <text class="skip-text">跳过 {{ Math.ceil(timeLeft / 1000) }}</text>
      </view>
      <view class="countdown-text" v-else>
        <text class="countdown-number">{{ Math.ceil(timeLeft / 1000) }}</text>
      </view>
    </view>

    <!-- 广告主体内容 -->
    <view class="ad-main-content">
      <!-- 广告卡片 -->
      <view class="ad-card">
        <view class="ad-header">
          <view class="ad-icon">
            <text class="icon-text">🚀</text>
          </view>
          <view class="ad-info">
            <text class="ad-title">{{ appName }}</text>
            <text class="ad-desc">专业去除视频水印，支持多平台</text>
            <view class="ad-source">
              <text class="source-icon">🔧</text>
              <text class="source-text">{{ appName }}</text>
            </view>
          </view>
          <view class="ad-action">
            <text class="action-btn">立即使用</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部应用信息 -->
    <view class="app-info">
      <view class="app-icon">
        <text class="app-icon-text">🎬</text>
      </view>
      <text class="app-name">{{ appName }}</text>
      <view class="loading-bar">
        <view class="loading-progress" :style="{ width: progressWidth }"></view>
      </view>
    </view>
  </view>
</template>

<script>
import appConfig from '../app-config.js'

export default {
  name: 'FullscreenAd',

  data() {
    return {
      visible: false,
      adData: {},
      timeLeft: 0,
      totalDuration: 0,
      timer: null,
      canClose: false,
      mustComplete: false,
      appName: appConfig.getAppName()
    }
  },
  
  computed: {
    progressWidth() {
      if (this.totalDuration === 0) return '0%'
      const progress = ((this.totalDuration - this.timeLeft) / this.totalDuration) * 100
      return Math.min(100, Math.max(0, progress)) + '%'
    }
  },
  
  methods: {
    showAd({ duration, title, description, canSkipAfter, mustComplete = false }) {
      this.adData = {
        title: title || '广告时间',
        description: description || '感谢您的支持'
      }
      
      this.totalDuration = duration
      this.timeLeft = duration
      this.canClose = false
      this.mustComplete = mustComplete
      this.visible = true
      
      // 开始倒计时
      this.startCountdown(canSkipAfter || duration)
    },
    
    startCountdown(canSkipAfter) {
      this.timer = setInterval(() => {
        this.timeLeft -= 100
        
        // 检查是否可以关闭
        if (this.timeLeft <= (this.totalDuration - canSkipAfter)) {
          this.canClose = true
        }
        
        // 广告播放完成 - 如果是必须手动关闭模式，则不自动关闭
        if (this.timeLeft <= 0) {
          if (this.mustComplete) {
            // 保持可关闭状态，不自动关闭
            this.timeLeft = 0
            this.canClose = true
          } else {
            this.completeAd()
          }
        }
      }, 100)
    },
    
    completeAd() {
      this.clearTimer()
      this.visible = false
      this.$emit('adComplete')
    },
    
    closeAd() {
      if (!this.canClose) return
      
      // 如果是必须完成模式，或者已经播放完成，触发完成事件
      if (this.mustComplete || this.timeLeft <= 0) {
        this.completeAd()
      } else if (this.timeLeft > 0) {
        // 如果没有完整观看，触发跳过事件
        this.clearTimer()
        this.visible = false
        this.$emit('adSkipped')
      }
    },
    
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    }
  },
  
  beforeDestroy() {
    this.clearTimer()
  }
}
</script>

<style scoped>
.fullscreen-ad-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 50%, #E1251B 100%);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-in;
}

/* 顶部状态栏 */
.ad-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: calc(20rpx + var(--status-bar-height, 44rpx));
  background: rgba(0, 0, 0, 0.1);
}

.ad-label {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}

.skip-button {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  backdrop-filter: blur(10rpx);
}

.skip-text {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 500;
}

.countdown-text {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  backdrop-filter: blur(10rpx);
}

.countdown-number {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
}

/* 广告主体内容 */
.ad-main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.ad-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10rpx);
}

.ad-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.ad-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #E1251B, #FF4142);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-text {
  font-size: 60rpx;
}

.ad-info {
  flex: 1;
}

.ad-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.ad-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
  line-height: 1.4;
}

.ad-source {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.source-icon {
  font-size: 20rpx;
}

.source-text {
  font-size: 22rpx;
  color: #999999;
}

.ad-action {
  flex-shrink: 0;
}

.action-btn {
  background: linear-gradient(135deg, #E1251B, #FF4142);
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 600;
  padding: 20rpx 32rpx;
  border-radius: 16rpx;
  display: block;
  text-align: center;
  min-width: 120rpx;
}

/* 底部应用信息 */
.app-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  gap: 20rpx;
  background: rgba(0, 0, 0, 0.1);
}

.app-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.app-icon-text {
  font-size: 40rpx;
}

.app-name {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

.loading-bar {
  flex: 1;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
  max-width: 200rpx;
}

.loading-progress {
  height: 100%;
  background: #FFFFFF;
  border-radius: 3rpx;
  transition: width 0.1s linear;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .ad-card {
    padding: 30rpx;
  }

  .ad-header {
    gap: 20rpx;
  }

  .ad-icon {
    width: 100rpx;
    height: 100rpx;
  }

  .icon-text {
    font-size: 50rpx;
  }

  .ad-title {
    font-size: 32rpx;
  }

  .ad-desc {
    font-size: 24rpx;
  }

  .action-btn {
    font-size: 26rpx;
    padding: 16rpx 24rpx;
  }
}
</style>
