{"id": "uni-cloud", "displayName": "hello uniCloud", "version": "1.0.0", "description": "为开发者提供的基于 serverless 模式和 js 编程的云开发平台。示例代码", "keywords": ["uniCloud示例"], "repository": "https://gitcode.net/dcloud/hellounicloud", "engines": {"HBuilderX": "^3.7.0", "uni-app": "^4.03", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-project", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}