# MarkEraser 配置参数详细说明

## 📋 文档说明

本文档提供 MarkEraser 项目所有配置参数的详细说明，包括参数含义、取值范围、使用场景和最佳实践。

**版本**: v1.8.0-stable  
**配置文件**: `components/ad-config.js`  
**更新时间**: 2025年01月20日

---

## 🎯 全局广告控制

### `globalEnabled`

**类型**: `Boolean`  
**默认值**: `true`  
**作用**: 全局广告开关，控制所有广告位的显示状态

```javascript
globalEnabled: true   // 开启所有广告
globalEnabled: false  // 关闭所有广告
```

**使用场景**:
- ✅ **开发调试**: 设为 `false` 快速关闭所有广告
- ✅ **紧急响应**: 出现问题时快速关闭广告系统
- ✅ **特殊活动**: 活动期间临时关闭广告
- ✅ **A/B测试**: 对比有无广告的用户行为

**优先级**: 最高，会覆盖所有单独的广告位配置

---

## 📱 广告位配置 (`adTypes`)

### 操作前广告 (`operation`)

```javascript
operation: {
  enabled: true,           // 是否启用操作前广告
  showInterval: 3,         // 每3次操作显示一次广告
  description: '用户执行解析操作前显示'
}
```

#### `enabled`
- **类型**: `Boolean`
- **默认值**: `true`
- **作用**: 控制操作前广告的显示
- **影响**: 用户点击解析按钮前是否显示广告

#### `showInterval`
- **类型**: `Number`
- **默认值**: `3`
- **取值范围**: `1-10`
- **作用**: 控制广告显示频率
- **计算方式**: 每 N 次操作显示一次广告

**频率建议**:
- `1`: 每次都显示（用户体验较差，但收益最高）
- `3`: 每3次显示一次（推荐，平衡体验和收益）
- `5`: 每5次显示一次（用户体验较好，收益适中）

### 进入广告 (`enter`)

```javascript
enter: {
  enabled: true,           // 是否启用进入广告
  showInterval: 1,         // 每次进入都显示
  description: '用户进入应用时显示'
}
```

#### `showInterval`
- **建议值**: `1`（每次进入都显示）
- **原因**: 进入频率相对较低，不会过度打扰用户

### 常驻广告 (`profile`)

```javascript
profile: {
  enabled: true,           // 是否启用常驻广告
  description: '个人中心页面常驻显示'
}
```

**特点**: 无 `showInterval` 参数，因为是常驻显示

### 结果页广告 (`result`)

```javascript
result: {
  enabled: true,           // 是否启用结果页广告
  description: '解析结果页面显示'
}
```

**显示时机**: 视频解析成功后在结果页面显示

---

## 🔐 权限管理配置 (`permissions`)

### `unlimitedAccessAfterAd`

**类型**: `Boolean`  
**默认值**: `true`  
**作用**: 是否启用"看广告获得无限权限"机制

```javascript
unlimitedAccessAfterAd: true   // 启用看广告获得权限
unlimitedAccessAfterAd: false  // 禁用此功能
```

**业务逻辑**:
- `true`: 用户观看广告后获得24小时无限解析权限
- `false`: 用户需要为每次解析观看广告

### `unlimitedAccessDuration`

**类型**: `Number`  
**默认值**: `24 * 60 * 60 * 1000` (24小时)  
**单位**: 毫秒  
**作用**: 无限权限的持续时间

```javascript
// 常用时间配置
unlimitedAccessDuration: 1 * 60 * 60 * 1000      // 1小时
unlimitedAccessDuration: 12 * 60 * 60 * 1000     // 12小时
unlimitedAccessDuration: 24 * 60 * 60 * 1000     // 24小时（推荐）
unlimitedAccessDuration: 7 * 24 * 60 * 60 * 1000 // 7天
```

**建议值**:
- **24小时**: 平衡用户体验和广告收益
- **12小时**: 增加广告展示频率
- **7天**: 提升用户满意度，但降低广告收益

---

## 🛡️ 防恶意请求配置 (`rateLimitConfig`)

### `enabled`

**类型**: `Boolean`  
**默认值**: `true`  
**作用**: 是否启用防恶意请求机制

```javascript
enabled: true   // 启用防护（生产环境推荐）
enabled: false  // 禁用防护（开发环境可用）
```

### `maxRequestsPerMinute`

**类型**: `Number`  
**默认值**: `10`  
**取值范围**: `5-50`  
**作用**: 每分钟最大请求数限制

```javascript
// 不同场景的建议值
maxRequestsPerMinute: 5   // 严格限制，适用于资源紧张
maxRequestsPerMinute: 10  // 标准限制，推荐值
maxRequestsPerMinute: 20  // 宽松限制，适用于高并发需求
maxRequestsPerMinute: 50  // 很宽松，主要防止极端滥用
```

**选择依据**:
- **服务器性能**: 性能越好，可设置越高
- **用户规模**: 用户越多，需要越严格的限制
- **业务需求**: 某些场景可能需要频繁解析

### `cooldownBetweenSameUrl`

**类型**: `Number`  
**默认值**: `10`  
**单位**: 秒  
**取值范围**: `5-300`  
**作用**: 同一URL的冷却时间

```javascript
// 不同场景的建议值
cooldownBetweenSameUrl: 5    // 开发测试环境
cooldownBetweenSameUrl: 10   // 当前设置，便于测试
cooldownBetweenSameUrl: 30   // 生产环境推荐
cooldownBetweenSameUrl: 60   // 严格限制
cooldownBetweenSameUrl: 300  // 极严格限制（5分钟）
```

**业务考虑**:
- **防止重复解析**: 避免用户重复解析同一视频
- **节省资源**: 减少不必要的服务器请求
- **用户体验**: 不能设置过长，影响正常使用

### `blockDuration`

**类型**: `Number`  
**默认值**: `60`  
**单位**: 秒  
**作用**: 触发频率限制后的阻断时间

```javascript
// 不同严格程度的设置
blockDuration: 30   // 轻度惩罚
blockDuration: 60   // 标准惩罚（推荐）
blockDuration: 120  // 中度惩罚
blockDuration: 300  // 重度惩罚（5分钟）
```

**设计原则**:
- **渐进式惩罚**: 可以考虑重复违规时增加惩罚时间
- **用户体验**: 不宜过长，避免误伤正常用户
- **威慑效果**: 足够长以起到防护作用

### 白名单配置 (`whitelist`)

```javascript
whitelist: {
  enabled: true,           // 是否启用白名单
  ips: [],                // IP白名单数组
  userIds: []             // 用户ID白名单数组
}
```

#### `enabled`
- **类型**: `Boolean`
- **作用**: 是否启用白名单机制

#### `ips`
- **类型**: `Array<String>`
- **格式**: IP地址字符串数组
- **示例**: `["*************", "********"]`

#### `userIds`
- **类型**: `Array<String>`
- **格式**: 用户ID字符串数组
- **示例**: `["admin", "test_user", "vip_001"]`

**使用场景**:
- **内部测试**: 开发团队IP或测试账号
- **VIP用户**: 付费用户或重要客户
- **合作伙伴**: API合作方的特殊账号
- **紧急情况**: 临时豁免特定用户

---

## 🔧 环境配置建议

### 开发环境

```javascript
const devConfig = {
  globalEnabled: false,              // 关闭广告便于开发
  rateLimitConfig: {
    enabled: false,                  // 关闭频率限制
    cooldownBetweenSameUrl: 5       // 短冷却时间便于测试
  },
  permissions: {
    unlimitedAccessAfterAd: false,   // 简化权限逻辑
    unlimitedAccessDuration: 1000 * 60 * 5  // 5分钟便于测试
  }
}
```

### 测试环境

```javascript
const testConfig = {
  globalEnabled: true,               // 测试广告功能
  adTypes: {
    operation: { showInterval: 1 },  // 每次都显示便于测试
    enter: { showInterval: 1 }
  },
  rateLimitConfig: {
    enabled: true,
    maxRequestsPerMinute: 20,        // 宽松限制便于测试
    cooldownBetweenSameUrl: 10,      // 短冷却时间
    blockDuration: 30                // 短惩罚时间
  }
}
```

### 生产环境

```javascript
const prodConfig = {
  globalEnabled: true,               // 开启所有广告
  adTypes: {
    operation: { showInterval: 3 },  // 平衡体验和收益
    enter: { showInterval: 1 }
  },
  rateLimitConfig: {
    enabled: true,
    maxRequestsPerMinute: 10,        // 标准限制
    cooldownBetweenSameUrl: 30,      // 合理冷却时间
    blockDuration: 60,               // 标准惩罚时间
    whitelist: {
      enabled: true,
      ips: ["内部IP地址"],
      userIds: ["admin", "vip_users"]
    }
  },
  permissions: {
    unlimitedAccessAfterAd: true,
    unlimitedAccessDuration: 24 * 60 * 60 * 1000  // 24小时
  }
}
```

---

## 📊 配置监控指标

### 广告相关指标

- **广告展示率**: `(广告展示次数 / 触发次数) * 100%`
- **广告点击率**: `(广告点击次数 / 广告展示次数) * 100%`
- **权限转化率**: `(获得权限用户数 / 观看广告用户数) * 100%`

### 频率限制指标

- **限制触发率**: `(被限制请求数 / 总请求数) * 100%`
- **误伤率**: `(正常用户被限制数 / 被限制总数) * 100%`
- **防护效果**: `阻止的恶意请求数量`

### 性能指标

- **平均响应时间**: 配置检查的耗时
- **内存使用**: 配置数据占用的内存
- **缓存命中率**: 配置缓存的效果

---

## 🚨 注意事项

### 配置修改风险

1. **生产环境修改**: 必须经过充分测试
2. **频率限制**: 过严可能误伤正常用户
3. **广告频率**: 过高影响用户体验
4. **权限时长**: 过长影响广告收益

### 最佳实践

1. **渐进式调整**: 小幅度调整参数，观察效果
2. **A/B测试**: 对比不同配置的效果
3. **用户反馈**: 关注用户对配置变更的反应
4. **数据驱动**: 基于实际数据调整配置

### 紧急处理

如果配置导致问题，可以：
1. 设置 `globalEnabled: false` 快速关闭广告
2. 设置 `rateLimitConfig.enabled: false` 关闭限制
3. 回滚到上一个稳定配置
4. 联系技术团队进行紧急修复

---

## 📞 技术支持

**配置问题咨询**: 参考项目文档或联系开发团队  
**紧急情况处理**: 优先使用全局开关进行快速响应  
**配置优化建议**: 基于实际业务数据进行调整

**维护团队**: MarkEraser 开发组  
**文档版本**: v1.8.0-stable  
**最后更新**: 2025年01月20日
