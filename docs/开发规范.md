# 开发规范

## 📋 核心开发规则

基于统一解析器架构，所有新平台模块开发必须严格遵循以下规则：

## 🚫 重要限制

### 公共页面和组件修改限制
**严格禁止修改公共页面和公共组件，除非获得明确授权**

- ❌ **禁止修改**：`pages/result/index.vue`、`components/` 目录下的所有组件
- ❌ **禁止修改**：公共的 JS 工具函数和配置文件
- ✅ **允许修改**：平台特定的解析器云函数
- ✅ **允许修改**：平台特定的页面（如 `pages/watermark-remover/index.vue`）

### 测试页面开发规范
**不要创建单独的test页面文件**

- ❌ **禁止**：创建 `pages/test/` 目录或测试页面
- ✅ **推荐**：在现有页面中添加测试功能
- ✅ **推荐**：使用控制台日志进行调试
- ✅ **推荐**：在开发完成后清理所有调试日志

### 修改公共代码的流程
如果需要修改公共页面或组件：

1. **必须先询问**：在修改前必须获得项目维护者的明确授权
2. **说明原因**：详细说明为什么需要修改公共代码
3. **提供方案**：提供多种解决方案供选择
4. **最小化修改**：如果必须修改，确保修改最小化

## 1. 网络请求规则 🌐

### User-Agent选择策略
**优先使用PC端User-Agent，移动端作为备选方案**

```javascript
// ✅ 推荐：优先使用PC端User-Agent（内容更全面）
const PC_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
  'Connection': 'keep-alive'
};

// ✅ 备选：PC端被反爬虫拦截时使用移动端
const MOBILE_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
  'Connection': 'keep-alive'
};

// ✅ 推荐的请求策略：PC优先，移动备用
async function requestWithFallback(url) {
  try {
    // 优先尝试PC端请求（内容更全面）
    console.log('尝试PC端请求...');
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      headers: PC_HEADERS,
      timeout: 15000,
      dataType: 'text'
    });
    console.log('PC端请求成功');
    return response;
  } catch (error) {
    console.log('PC端请求失败，尝试移动端...', error.message);
    // PC端失败时使用移动端
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET', 
      headers: MOBILE_HEADERS,
      timeout: 15000,
      dataType: 'text'
    });
    console.log('移动端请求成功');
    return response;
  }
}
```

### User-Agent选择原则
- **PC端优先**：PC版本的网页通常包含更完整的数据结构
- **移动端备用**：当PC端被反爬虫机制拦截时使用
- **平台差异**：
  - 小红书：PC端数据更全面，优先使用PC端
  - 抖音：根据实际测试选择最优策略
  - 快手：根据数据完整性选择合适的User-Agent

### 请求方式要求
- **必须**：使用 `uniCloud.httpclient.request()`
- **必须**：设置合理的timeout（建议15000ms）
- **必须**：开启 `followRedirect: true`
- **建议**：使用 `dataType: 'text'`

## 2. 数据提取规则 📊

### 必须使用正则表达式提取JSON
```javascript
// ✅ 正确示例
const JSON_PATTERNS = [
  /window\.__INITIAL_STATE__\s*=\s*({.+?})<\/script>/s,
  /window\.pageData\s*=\s*({.+?})<\/script>/s,
  /window\._sharedData\s*=\s*({.+?})<\/script>/s
];

function extractJsonFromHtml(html) {
  for (const pattern of JSON_PATTERNS) {
    const match = html.match(pattern);
    if (match) {
      try {
        return JSON.parse(match[1]);
      } catch (error) {
        console.log('JSON解析失败:', error.message);
      }
    }
  }
  return null;
}

// ❌ 错误示例 - 禁止硬编码数据位置
const hardcodedStart = html.indexOf('{"data":');
const hardcodedEnd = html.indexOf('}</script>', hardcodedStart);
```

### 数据字段提取要求
必须提取的字段：
- **title** - 内容标题
- **author** - 作者名称
- **content** - 内容描述/正文
- **videoUrl** - 视频链接（如有）
- **imageUrls** - 图片链接数组（如有）
- **coverUrl** - 封面图链接

## 3. 调试开发规则 🐛

### 开发阶段：允许生成调试文件
```javascript
// ✅ 开发时允许
function saveDebugFiles(html, jsonData, contentId) {
  if (process.env.NODE_ENV === 'development') {
    const fs = require('fs');
    
    // 生成HTML源代码文件
    fs.writeFileSync(`/tmp/debug_${contentId}_page.html`, html);
    
    // 生成JSON数据文件
    fs.writeFileSync(`/tmp/debug_${contentId}_data.json`, 
      JSON.stringify(jsonData, null, 2));
    
    // 生成解析结果文件
    fs.writeFileSync(`/tmp/debug_${contentId}_result.json`, 
      JSON.stringify(parseResult, null, 2));
    
    console.log('=== 调试文件已生成 ===');
    console.log(`HTML源代码: debug_${contentId}_page.html`);
    console.log(`JSON数据: debug_${contentId}_data.json`);
    console.log(`解析结果: debug_${contentId}_result.json`);
  }
}

// ✅ 开发时允许详细日志
console.log('=== 页面源代码前500字符 ===');
console.log(html.substring(0, 500));
console.log('=== JSON数据结构 ===');
console.log(JSON.stringify(jsonData, null, 2));
```

### 生产环境：必须删除调试代码
```javascript
// ❌ 生产环境禁止 - 必须删除
fs.writeFileSync('/tmp/debug.html', html);           // 删除文件生成
console.log('页面源代码:', html);                      // 删除详细日志
console.log('完整JSON数据:', jsonData);               // 删除数据打印
console.log('调试信息:', debugInfo);                  // 删除调试信息

// ✅ 生产环境允许 - 保留关键日志
console.log('解析成功，标题:', result.title);
console.log('提取到视频URL:', result.videoUrl);
console.error('解析失败:', error.message);
```

### 生产就绪检查清单
- [ ] 删除所有 `fs.writeFileSync()` 文件生成代码
- [ ] 删除详细的HTML源代码打印
- [ ] 删除完整JSON数据打印
- [ ] 删除调试用的临时变量和函数
- [ ] 保留必要的成功/失败日志
- [ ] 删除开发时的测试URL和测试数据

## 4. 兼容性保证规则 🔒

### 严格遵循标准返回格式
```javascript
// ✅ 必须的标准格式
exports.main = async (event, context) => {
  try {
    const result = await parseContent(event.link);
    
    return {
      success: true,
      data: {
        // 必需字段
        title: result.title,
        author: result.author,
        content: result.content,
        type: result.type,           // 'video' | 'image' | 'image_text'
        platform: 'platform-name',
        source: '平台中文名',
        originalUrl: event.link,
        
        // 媒体字段
        processedData: result.processedData,
        coverUrl: result.coverUrl,
        
        // 可选字段（根据平台特点添加）
        videoUrl: result.videoUrl,
        imageUrls: result.imageUrls,
        duration: result.duration,
        viewCount: result.viewCount
      },
      timestamp: new Date().toISOString(),
      version: "平台解析器 v1.0.0"
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
      error: undefined  // 不暴露详细错误信息
    };
  }
};

// ❌ 禁止的格式变更
return result.data;                    // 缺少success标识
return { result: data };               // 格式不标准
return { ...data, custom: 'field' };   // 直接扩展根对象
```

### 统一解析器配置要求
```javascript
// ✅ 在 unified-parser/index.js 中正确配置
newplatform: {
  name: '平台中文名',              // 必须：用户可见的平台名称
  parser: 'platform-parser',     // 必须：云函数名称
  patterns: [                    // 必须：URL匹配模式数组
    /platform\.com/i,
    /p\.com/i,                   // 短链接等变体
    /mobile\.platform\.com/i
  ],
  defaultOptions: {              // 必须：默认选项
    forceRemoveWatermark: true,  // 是否去水印
    debug: false                 // 是否开启调试
  }
}

// ❌ 禁止的配置错误
newplatform: 'platform-parser'  // 缺少配置对象
{ name: '平台', parser: 'xxx' }   // 缺少patterns或defaultOptions
{ patterns: 'platform.com' }     // patterns必须是数组
```

## 5. 代码架构规则 🏗️

### 必须参考现有架构，禁止参考解析逻辑
```javascript
// ✅ 可以参考的架构要素
- 文件目录结构：package.json + index.js
- 函数组织方式：验证 -> 请求 -> 提取 -> 解析 -> 返回
- 错误处理模式：try-catch + 标准错误格式
- 代码注释风格：JSDoc格式注释
- 变量命名规范：驼峰命名法

// ❌ 禁止参考的解析细节
- 具体的正则表达式模式（每个平台不同）
- JSON数据结构的访问路径（每个平台不同）
- 特定的URL处理逻辑（每个平台不同）
- 平台特有的参数和配置（每个平台不同）
```

### 独立模块要求
```javascript
// ✅ 模块独立性要求
- 云函数独立部署，不依赖其他解析器
- 错误不影响其他平台功能
- 可以独立测试和调试
- 可以独立更新版本

// ❌ 禁止的相互依赖
- 不能调用其他平台的解析器函数
- 不能共享具体的解析逻辑代码
- 不能修改unified-parser之外的现有代码
- 不能影响前端代码的运行
```

## 6. 质量标准规则 ✅

### 性能要求
- **响应时间**：正常情况下 < 5秒
- **超时处理**：网络请求设置15秒超时
- **内存使用**：避免大量HTML/JSON数据常驻内存
- **错误恢复**：网络错误时提供友好错误信息

### 安全要求
- **输入验证**：验证链接格式和来源
- **错误信息**：不暴露敏感系统信息
- **注入防护**：使用安全的JSON解析方法
- **访问控制**：不访问不必要的系统资源

### 测试要求
- **功能测试**：直接在主页面（pages/watermark-remover/index.vue）输入链接进行测试
- **集成测试**：通过unified-parser的完整流程测试
- **边界测试**：异常链接和边界情况处理
- **回归测试**：确保不影响现有平台功能

### 测试方式说明
- **❌ 不需要创建单独的测试页面**：避免pages.json配置冗余
- **✅ 推荐使用主页面测试**：直接在去水印页面输入对应平台链接测试
- **✅ 调试信息查看**：开启debug模式查看解析过程和生成的调试文件
- **✅ 云函数直接测试**：在HBuilderX中右键云函数选择"本地运行"测试

## 🚨 违规后果

### 严重违规（项目影响）
- **影响现有功能** → 立即回滚，重新开发
- **破坏前端兼容性** → 架构修复，代码重构
- **数据格式不标准** → 结果页面异常，用户体验受损

### 一般违规（代码质量）
- **未删除调试代码** → 代码审查不通过，需要清理
- **未使用合适的User-Agent策略** → 可能被平台拦截，解析失败
- **错误处理不完整** → 生产环境可能崩溃

## ✅ 开发成功标准

### 功能验证
- [ ] 能够解析目标平台的分享链接
- [ ] 正确提取标题、作者、媒体URL等信息
- [ ] 返回标准格式的数据结构
- [ ] 通过统一解析器的集成测试
- [ ] 前端结果页面正常显示内容
- [ ] 在主页面实际测试链接解析成功

### 代码质量
- [ ] 优先使用PC端User-Agent，移动端作为备用
- [ ] 使用正则表达式提取JSON数据
- [ ] 删除所有调试文件生成代码
- [ ] 完整的错误处理和日志记录
- [ ] 遵循现有代码的架构模式

### 架构兼容
- [ ] 在unified-parser中正确配置路由
- [ ] 不影响任何现有平台功能
- [ ] 前端无需修改即可支持新平台
- [ ] 结果页面完全兼容现有格式

**记住：严格遵循开发规则是确保项目稳定性和可扩展性的基础！** 🛡️