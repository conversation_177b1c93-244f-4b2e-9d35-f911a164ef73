# 📚 MarkEraser 项目文档

## 文档概览

### 🚀 项目介绍
- **项目简介.md** - 项目的基本介绍和功能说明
- **项目说明.md** - 详细的项目架构和使用指导（Claude Code专用）

### 🏗️ 架构文档  
- **架构升级说明.md** - 统一解析器架构升级详细说明
- **代码优化策略.md** - 代码清理和优化的策略方法

### 👨‍💻 开发文档
- **新模块开发指南.md** - 新平台解析模块的完整开发指南
- **开发规范.md** - 严格的开发规则和质量标准

### 📊 项目管理
- **更新日志.md** - 项目版本更新记录
- **数据库设计.md** - 数据库结构和设计说明

## 📖 文档使用指引

### 新手入门
1. 先阅读 **项目简介.md** 了解项目基本情况
2. 查看 **项目说明.md** 了解技术架构
3. 参考 **架构升级说明.md** 了解最新架构

### 开发新平台
1. 必读 **开发规范.md** 了解严格的开发规则
2. 参考 **新模块开发指南.md** 进行具体开发  
3. 遵循 **代码优化策略.md** 进行代码清理

### 重要提醒
- **User-Agent策略**：优先使用PC端，移动端作为备用
- **数据完整性**：PC端网页通常包含更完整的数据结构
- **反爬虫应对**：PC端被拦截时自动切换到移动端

### 项目维护
1. 查看 **更新日志.md** 了解版本变更
2. 参考 **数据库设计.md** 了解数据结构
3. 应用 **代码优化策略.md** 进行代码维护

## 🎯 核心优势

### 统一解析器架构
- **前端零修改扩展** - 新平台接入时前端代码无需变动
- **配置驱动开发** - 新平台只需添加配置，不需编码
- **智能路由分发** - 自动检测平台并调用对应解析器

### 开发效率提升
- **接入时间**：3-4小时 → 30分钟 (85%+ 提升)
- **代码修改**：前端+后端 → 仅后端配置
- **维护成本**：大幅降低，集中管理

### 质量保证体系
- **严格开发规范** - 确保代码质量和功能稳定
- **完整测试流程** - 从开发到生产的质量检查
- **向后兼容保证** - 新功能不影响现有功能

## 📞 支持与反馈

- **技术问题**：参考对应的技术文档
- **开发规范**：严格遵循开发规范文档
- **架构疑问**：查看架构升级说明文档

---

**让平台扩展变得简单：写配置，不写代码！** 🚀