# 版本更新说明

## v1.3.0 (2025-01-09) - 新增B站平台支持

### 🎉 主要更新

#### 新平台支持
- 🆕 **新增B站平台支持**：完整支持B站动态、专栏等内容解析
- 🆕 **Live Photo功能**：B站Live Photo内容完美支持
- 🆕 **多格式支持**：支持B站动态、专栏、视频等多种内容类型

#### 功能完善
- ✅ **修复B站Live Photo显示问题**：修复了B站解析器Live Photo数据结构，与小红书保持一致
- ✅ **优化调试日志**：清理了大量调试日志，减少控制台输出，提升用户体验
- ✅ **完善开发规范**：更新了开发规范文档，明确了公共页面修改限制

#### 技术改进
- 🔧 **B站解析器优化**：
  - 添加`videoUrls`字段，只包含有效的视频URL
  - 添加`hasLivePhoto`字段，正确判断是否有Live Photo
  - 保持`livePhotoVideos`数组结构，与图片数量一致
- 🔧 **代码结构优化**：提高代码可维护性和一致性

#### 文档更新
- 📚 **开发规范完善**：
  - 添加公共页面和组件修改限制
  - 规定不要创建单独的test页面文件
  - 明确修改公共代码的流程和授权要求
  - 强调最小化修改原则

### 🚫 重要限制

#### 公共页面修改限制
- ❌ **禁止修改**：`pages/result/index.vue`、`components/` 目录下的所有组件
- ❌ **禁止修改**：公共的 JS 工具函数和配置文件
- ✅ **允许修改**：平台特定的解析器云函数
- ✅ **允许修改**：平台特定的页面

#### 测试页面开发规范
- ❌ **禁止**：创建 `pages/test/` 目录或测试页面
- ✅ **推荐**：在现有页面中添加测试功能
- ✅ **推荐**：使用控制台日志进行调试
- ✅ **推荐**：在开发完成后清理所有调试日志

### 🔄 修改公共代码的流程
如果需要修改公共页面或组件：
1. **必须先询问**：在修改前必须获得项目维护者的明确授权
2. **说明原因**：详细说明为什么需要修改公共代码
3. **提供方案**：提供多种解决方案供选择
4. **最小化修改**：如果必须修改，确保修改最小化

### 📋 版本历史

#### v1.2.0 (2025-01-08) - 三平台稳定版
- 统一解析器架构完善
- 支持抖音、快手、小红书三大平台
- Live Photo功能实现

#### v1.1.0 (2025-01-07)
- 新增B站平台支持
- Live Photo功能实现

#### v1.0.0 (2025-01-06)
- 项目初始版本
- 基础功能实现

### 🎉 主要更新

#### 功能完善
- ✅ **修复B站Live Photo显示问题**：修复了B站解析器Live Photo数据结构，与小红书保持一致
- ✅ **优化调试日志**：清理了大量调试日志，减少控制台输出，提升用户体验
- ✅ **完善开发规范**：更新了开发规范文档，明确了公共页面修改限制

#### 技术改进
- 🔧 **B站解析器优化**：
  - 添加`videoUrls`字段，只包含有效的视频URL
  - 添加`hasLivePhoto`字段，正确判断是否有Live Photo
  - 保持`livePhotoVideos`数组结构，与图片数量一致
- 🔧 **代码结构优化**：提高代码可维护性和一致性

#### 文档更新
- 📚 **开发规范完善**：
  - 添加公共页面和组件修改限制
  - 规定不要创建单独的test页面文件
  - 明确修改公共代码的流程和授权要求
  - 强调最小化修改原则

### 🚫 重要限制

#### 公共页面修改限制
- ❌ **禁止修改**：`pages/result/index.vue`、`components/` 目录下的所有组件
- ❌ **禁止修改**：公共的 JS 工具函数和配置文件
- ✅ **允许修改**：平台特定的解析器云函数
- ✅ **允许修改**：平台特定的页面

#### 测试页面开发规范
- ❌ **禁止**：创建 `pages/test/` 目录或测试页面
- ✅ **推荐**：在现有页面中添加测试功能
- ✅ **推荐**：使用控制台日志进行调试
- ✅ **推荐**：在开发完成后清理所有调试日志

### 🔄 修改公共代码的流程
如果需要修改公共页面或组件：
1. **必须先询问**：在修改前必须获得项目维护者的明确授权
2. **说明原因**：详细说明为什么需要修改公共代码
3. **提供方案**：提供多种解决方案供选择
4. **最小化修改**：如果必须修改，确保修改最小化

### 📋 版本历史

#### v1.2.1 (2025-01-09)
- 修复B站Live Photo显示问题
- 优化调试日志输出
- 完善代码结构

#### v1.2.0 (2025-01-08)
- 统一解析器架构完善
- 多平台支持优化

#### v1.1.0 (2025-01-07)
- 新增B站平台支持
- Live Photo功能实现

#### v1.0.0 (2025-01-06)
- 项目初始版本
- 基础功能实现
