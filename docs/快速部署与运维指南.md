# MarkEraser 快速部署与运维指南

## 📋 文档概述

本文档提供 MarkEraser 项目的快速部署流程、运维检查清单和常见问题解决方案。

**版本**: v1.8.0-stable  
**适用环境**: 生产环境、测试环境  
**更新时间**: 2025年01月20日

---

## 🚀 快速部署流程

### 1. 环境准备

#### 开发工具要求
- **HBuilder X**: 最新版本
- **微信开发者工具**: 最新稳定版
- **Node.js**: v14.0.0 或更高版本
- **Git**: 用于代码管理

#### 云服务要求
- **uniCloud**: 阿里云版本
- **云函数**: 支持 Node.js 运行时
- **云数据库**: 用于存储用户数据（可选）

### 2. 代码部署

#### 获取稳定版本
```bash
# 克隆项目
git clone https://gitcode.com/zt8890149/MarkEraser.git

# 切换到稳定版本
cd MarkEraser
git checkout v1.8.0-stable

# 查看版本信息
git tag -l | grep stable
```

#### 项目结构检查
```
MarkEraser/
├── components/           # 组件目录
│   ├── ad-config.js     # 广告配置（重要）
│   ├── ad-manager.js    # 广告管理器
│   └── ...
├── pages/               # 页面目录
├── uniCloud-aliyun/     # 云函数目录
│   └── cloudfunctions/
│       └── video-parser/
└── docs/                # 文档目录
```

### 3. 配置检查

#### 核心配置文件
检查 `components/ad-config.js` 中的关键配置：

```javascript
// 生产环境推荐配置
const prodConfig = {
  globalEnabled: true,                    // ✅ 开启广告
  
  rateLimitConfig: {
    enabled: true,                        // ✅ 开启防护
    maxRequestsPerMinute: 10,            // ✅ 合理限制
    cooldownBetweenSameUrl: 30,          // ✅ 30秒冷却
    blockDuration: 60                     // ✅ 60秒惩罚
  },
  
  permissions: {
    unlimitedAccessAfterAd: true,         // ✅ 开启权限机制
    unlimitedAccessDuration: 24 * 60 * 60 * 1000  // ✅ 24小时
  }
}
```

#### 云函数配置
检查 `uniCloud-aliyun/cloudfunctions/video-parser/index.js`：

```javascript
// 确保云函数正常配置
exports.main = async (event, context) => {
  // 检查事件参数
  const { url, userId } = event
  
  // 确保有适当的错误处理
  try {
    // 业务逻辑
  } catch (error) {
    console.error('云函数错误:', error)
    return { success: false, error: error.message }
  }
}
```

### 4. 部署步骤

#### 步骤1: 云函数部署
1. 在 HBuilder X 中打开项目
2. 右键 `uniCloud-aliyun` → 关联云服务空间
3. 右键 `video-parser` → 上传并运行

#### 步骤2: 前端部署
1. 配置小程序 AppID
2. 运行到微信开发者工具
3. 预览和调试功能
4. 提交审核和发布

#### 步骤3: 功能验证
- [ ] 视频解析功能正常
- [ ] 广告正常显示
- [ ] 频率限制生效
- [ ] 权限机制工作
- [ ] 倒计时弹窗正常

---

## ✅ 部署检查清单

### 配置检查
- [ ] `globalEnabled` 设置为 `true`
- [ ] 频率限制参数合理设置
- [ ] 权限机制配置正确
- [ ] 白名单配置（如需要）
- [ ] 云函数配置无误

### 功能检查
- [ ] 视频解析功能测试通过
- [ ] 广告展示正常
- [ ] 频率限制触发正常
- [ ] 动态倒计时显示正确
- [ ] 权限获取和验证正常

### 性能检查
- [ ] 页面加载速度正常
- [ ] 云函数响应时间合理
- [ ] 内存使用在正常范围
- [ ] 无明显的性能瓶颈

### 安全检查
- [ ] 防恶意请求机制生效
- [ ] 用户数据安全存储
- [ ] 敏感信息正确处理
- [ ] 错误信息不泄露敏感数据

---

## 🔧 运维监控

### 1. 关键指标监控

#### 业务指标
```javascript
// 需要监控的关键指标
const metrics = {
  // 解析成功率
  parseSuccessRate: '解析成功次数 / 总解析次数',
  
  // 广告展示率
  adDisplayRate: '广告展示次数 / 广告触发次数',
  
  // 频率限制触发率
  rateLimitTriggerRate: '被限制请求数 / 总请求数',
  
  // 用户活跃度
  activeUsers: '日活跃用户数',
  
  // 权限转化率
  permissionConversionRate: '获得权限用户数 / 观看广告用户数'
}
```

#### 技术指标
- **云函数调用次数**: 监控API使用量
- **云函数执行时间**: 监控性能
- **错误率**: 监控系统稳定性
- **并发用户数**: 监控负载情况

### 2. 日志监控

#### 关键日志
```javascript
// 重要的日志记录点
console.log('[解析请求]', { userId, url, timestamp })
console.log('[频率限制]', { userId, reason, waitTime })
console.log('[广告展示]', { adType, userId, timestamp })
console.log('[权限获取]', { userId, duration, timestamp })
console.error('[系统错误]', { error, context, timestamp })
```

#### 日志分析
- **错误日志**: 及时发现和处理问题
- **性能日志**: 识别性能瓶颈
- **业务日志**: 分析用户行为
- **安全日志**: 监控异常访问

### 3. 告警设置

#### 告警规则
```javascript
// 建议的告警阈值
const alertThresholds = {
  errorRate: 5,              // 错误率超过5%
  responseTime: 3000,        // 响应时间超过3秒
  rateLimitTrigger: 20,      // 频率限制触发率超过20%
  adDisplayFailure: 10       // 广告展示失败率超过10%
}
```

#### 告警渠道
- **邮件通知**: 发送给运维团队
- **短信通知**: 紧急情况通知
- **钉钉/企微**: 团队群组通知
- **监控面板**: 实时状态展示

---

## 🚨 故障处理

### 1. 常见问题及解决方案

#### 广告不显示
**症状**: 用户反馈看不到广告
**排查步骤**:
1. 检查 `globalEnabled` 是否为 `true`
2. 检查具体广告位的 `enabled` 配置
3. 验证 `shouldShowAd()` 方法逻辑
4. 检查广告组件是否正确引入

**解决方案**:
```javascript
// 临时开启所有广告
adConfig.globalEnabled = true
adConfig.adTypes.operation.enabled = true
adConfig.adTypes.enter.enabled = true
```

#### 频率限制异常
**症状**: 用户被错误限制或限制不生效
**排查步骤**:
1. 检查 `rateLimitConfig.enabled` 设置
2. 验证时间戳计算逻辑
3. 检查用户ID和URL记录
4. 查看限制触发日志

**解决方案**:
```javascript
// 临时关闭频率限制
adConfig.rateLimitConfig.enabled = false

// 或调整限制参数
adConfig.rateLimitConfig.maxRequestsPerMinute = 20
adConfig.rateLimitConfig.cooldownBetweenSameUrl = 10
```

#### 云函数异常
**症状**: 解析功能不工作
**排查步骤**:
1. 检查云函数日志
2. 验证云函数配置
3. 测试云函数连通性
4. 检查参数传递

**解决方案**:
1. 重新部署云函数
2. 检查云服务空间配置
3. 验证网络连接
4. 回滚到稳定版本

#### 权限异常
**症状**: 用户权限获取或验证失败
**排查步骤**:
1. 检查权限数据存储
2. 验证时间戳计算
3. 检查权限检查逻辑
4. 查看权限相关日志

**解决方案**:
```javascript
// 临时延长权限时间
adConfig.permissions.unlimitedAccessDuration = 48 * 60 * 60 * 1000

// 或临时关闭权限验证
adConfig.permissions.unlimitedAccessAfterAd = false
```

### 2. 紧急处理流程

#### 紧急关闭广告
```javascript
// 一键关闭所有广告
adConfig.globalEnabled = false
```

#### 紧急关闭防护
```javascript
// 一键关闭频率限制
adConfig.rateLimitConfig.enabled = false
```

#### 紧急回滚
```bash
# 回滚到上一个稳定版本
git checkout v1.7.0
# 重新部署
```

### 3. 故障预防

#### 配置备份
```javascript
// 定期备份配置
const configBackup = JSON.stringify(adConfig)
localStorage.setItem('adConfig_backup', configBackup)
```

#### 灰度发布
1. 先在测试环境验证
2. 小范围用户测试
3. 逐步扩大范围
4. 全量发布

#### 监控预警
- 设置合理的告警阈值
- 建立快速响应机制
- 定期检查系统状态
- 保持团队沟通畅通

---

## 📊 性能优化

### 1. 配置优化

#### 缓存策略
```javascript
// 配置缓存优化
const configCache = {
  adConfig: null,
  lastUpdate: 0,
  cacheTimeout: 5 * 60 * 1000  // 5分钟缓存
}

function getCachedConfig() {
  const now = Date.now()
  if (configCache.adConfig && (now - configCache.lastUpdate) < configCache.cacheTimeout) {
    return configCache.adConfig
  }
  
  // 重新加载配置
  configCache.adConfig = loadAdConfig()
  configCache.lastUpdate = now
  return configCache.adConfig
}
```

#### 懒加载
```javascript
// 广告组件懒加载
const lazyLoadAd = () => {
  return import('./components/operation-ad/operation-ad.vue')
}
```

### 2. 代码优化

#### 减少不必要的计算
```javascript
// 优化频率检查
const rateLimitCache = new Map()

function checkRateLimit(userId) {
  const cacheKey = `${userId}_${Math.floor(Date.now() / 60000)}`
  if (rateLimitCache.has(cacheKey)) {
    return rateLimitCache.get(cacheKey)
  }
  
  const result = performRateLimitCheck(userId)
  rateLimitCache.set(cacheKey, result)
  return result
}
```

#### 内存管理
```javascript
// 定期清理过期数据
setInterval(() => {
  cleanupExpiredData()
}, 60 * 60 * 1000)  // 每小时清理一次
```

---

## 📞 技术支持

### 联系方式
- **技术文档**: 查看 `docs/` 目录下的详细文档
- **问题反馈**: 通过项目 Issues 提交问题
- **紧急联系**: 联系项目维护团队

### 支持资源
- **部署文档**: 本文档
- **配置文档**: `docs/配置参数详细说明.md`
- **架构文档**: `docs/广告框架与安全机制技术文档.md`
- **开发文档**: `docs/开发规范.md`

### 版本信息
- **当前稳定版**: v1.8.0-stable
- **发布时间**: 2025年01月20日
- **维护团队**: MarkEraser 开发组
- **下次更新**: 根据需求和反馈确定

---

## 📝 更新记录

### v1.8.0-stable (2025-01-20)
- ✅ 完善部署流程文档
- ✅ 添加运维监控指南
- ✅ 完善故障处理流程
- ✅ 优化性能建议

### 后续计划
- 🔄 持续优化部署流程
- 🔄 完善监控告警机制
- 🔄 增强故障自动恢复能力
- 🔄 提升系统整体稳定性

**文档维护**: MarkEraser 开发组  
**最后更新**: 2025年01月20日
