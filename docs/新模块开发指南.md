# 新模块开发指南

## 📋 概述

本指南用于指导开发者为 MarkEraser 项目添加新的短视频平台解析模块。基于统一解析器架构，新增平台时**前端代码零修改**，只需要开发后端解析器模块。

## 🏗️ 开发流程

### 步骤 1：创建解析器云函数

在 `uniCloud-aliyun/cloudfunctions/` 目录下创建新的解析器：

```bash
mkdir uniCloud-aliyun/cloudfunctions/[platform-name]-parser
```

### 步骤 2：配置解析器到统一入口

在 `unified-parser/index.js` 的 `PLATFORM_CONFIG` 中添加配置：

```javascript
[platform-name]: {
  name: '平台中文名',
  parser: '[platform-name]-parser',
  patterns: [
    /platform\.com/i,
    /p\.com/i  // 短链接等
  ],
  defaultOptions: {
    forceRemoveWatermark: true,  // 是否去水印
    debug: false                 // 是否开启调试
  }
}
```

### 步骤 3：前端自动支持

配置完成后，前端自动支持新平台，无需修改任何代码！

## 🔧 开发规则

### 🚫 重要限制

#### 公共页面和组件修改限制
**严格禁止修改公共页面和公共组件，除非获得明确授权**

- ❌ **禁止修改**：`pages/result/index.vue`、`components/` 目录下的所有组件
- ❌ **禁止修改**：公共的 JS 工具函数和配置文件
- ✅ **允许修改**：平台特定的解析器云函数
- ✅ **允许修改**：平台特定的页面（如 `pages/watermark-remover/index.vue`）

#### 测试页面开发规范
**不要创建单独的test页面文件**

- ❌ **禁止**：创建 `pages/test/` 目录或测试页面
- ✅ **推荐**：在现有页面中添加测试功能
- ✅ **推荐**：使用控制台日志进行调试
- ✅ **推荐**：在开发完成后清理所有调试日志

#### 修改公共代码的流程
如果需要修改公共页面或组件：

1. **必须先询问**：在修改前必须获得项目维护者的明确授权
2. **说明原因**：详细说明为什么需要修改公共代码
3. **提供方案**：提供多种解决方案供选择
4. **最小化修改**：如果必须修改，确保修改最小化

### 1. 网络请求规范

**优先使用PC端User-Agent，移动端作为备选方案：**

```javascript
// PC端请求头（推荐优先使用）
const PC_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
  'Accept-Encoding': 'gzip, deflate, br',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1'
};

// 移动端请求头（备用）
const MOBILE_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
  'Accept-Encoding': 'gzip, deflate',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1'
};

// 推荐的请求方式：PC优先，移动备用
async function fetchPageContent(url) {
  try {
    // 优先尝试PC端（数据通常更完整）
    console.log('尝试PC端请求...');
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      followRedirect: true,
      timeout: 15000,
      dataType: 'text',
      headers: PC_HEADERS
    });
    console.log('PC端请求成功');
    return response;
  } catch (error) {
    console.log('PC端被拦截，尝试移动端...', error.message);
    // PC端失败时使用移动端
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      followRedirect: true,  
      timeout: 15000,
      dataType: 'text',
      headers: MOBILE_HEADERS
    });
    console.log('移动端请求成功');
    return response;
  }
}
```

### 2. 数据提取规范

**通过正则表达式提取页面JSON数据：**

```javascript
// 1. 提取常见的数据源
const patterns = [
  /window\.__INITIAL_STATE__\s*=\s*({.+?})<\/script>/s,
  /window\.pageData\s*=\s*({.+?})<\/script>/s,
  /window\._sharedData\s*=\s*({.+?})<\/script>/s,
  // 根据实际平台添加更多模式
];

// 2. 尝试匹配并解析JSON
for (const pattern of patterns) {
  const match = html.match(pattern);
  if (match) {
    try {
      const jsonData = JSON.parse(match[1]);
      return extractDataFromJson(jsonData);
    } catch (error) {
      console.log('JSON解析失败:', error.message);
    }
  }
}

// 3. 从JSON中提取必要信息
function extractDataFromJson(jsonData) {
  return {
    title: jsonData.title || '未知标题',
    author: jsonData.author || '未知作者',
    content: jsonData.description || '',
    videoUrl: jsonData.video?.url || null,
    coverUrl: jsonData.cover?.url || null,
    imageUrls: jsonData.images?.map(img => img.url) || [],
    // ... 其他需要的字段
  };
}
```

### 3. 调试开发规范

**开发阶段可以生成调试文件，完成后必须删除：**

```javascript
// ✅ 开发阶段：生成调试文件
async function saveDebugFiles(html, jsonData, contentId) {
  // 保存HTML源代码
  const fs = require('fs');
  fs.writeFileSync(`/tmp/debug_${contentId}_page.html`, html);
  
  // 保存JSON数据
  fs.writeFileSync(`/tmp/debug_${contentId}_data.json`, JSON.stringify(jsonData, null, 2));
  
  console.log(`调试文件已保存: debug_${contentId}_*`);
}

// ❌ 生产环境：必须删除所有调试代码
// 包括：文件生成、详细日志打印、测试代码等
```

### 4. 兼容性保证

**严格遵循标准返回格式，确保不影响现有功能：**

```javascript
// 标准返回格式
exports.main = async (event, context) => {
  try {
    const result = await parseContent(event.link);
    
    return {
      success: true,
      data: {
        title: result.title,
        author: result.author, 
        content: result.content,
        processedData: result.processedData,
        type: result.type, // 'video' | 'image' | 'image_text'
        platform: 'platform-name',
        source: '平台中文名',
        originalUrl: event.link,
        coverUrl: result.coverUrl,
        // 其他平台特有字段...
      },
      timestamp: new Date().toISOString(),
      version: "平台解析器 v1.0.0"
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
      error: undefined  // 不暴露敏感错误信息
    };
  }
};
```

## 📚 参考实现

### 参考现有解析器架构（不要参考解析逻辑）

1. **快手解析器** (`simple-kuaishou-parser/`) - 简洁的架构参考
2. **小红书解析器** (`xiaohongshu-parser/`) - 多媒体内容处理参考  
3. **抖音解析器** (`simple-douyin-parser/`) - 基础功能参考

**注意：只参考文件结构和代码组织方式，解析逻辑每个平台都不同！**

### 代码结构模板

```javascript
'use strict';

/**
 * [平台名]解析器 v1.0.0
 * 支持[平台名]分享链接的内容解析
 */

// 移动端请求头
const MOBILE_HEADERS = {
  'User-Agent': '...',  // 优先PC端，移动端备用
  // ... 其他头信息
};

// 验证链接是否为目标平台
function isValidLink(link) {
  const patterns = [
    /platform\.com/i,
    /p\.com/i
  ];
  return patterns.some(pattern => pattern.test(link));
}

// 清理和处理链接
function cleanLink(link) {
  // 提取和清理URL逻辑
}

// 从HTML提取JSON数据
function extractJsonFromHtml(html) {
  // 正则表达式提取JSON逻辑
}

// 从JSON解析内容信息
function parseContentFromJson(jsonData) {
  // 解析逻辑，提取标题、作者、媒体URL等
}

// 主解析函数
async function parseContent(shareUrl) {
  // 1. 验证和清理链接
  // 2. 请求页面源代码
  // 3. 提取JSON数据
  // 4. 解析内容信息
  // 5. 返回标准格式
}

// 云函数入口
exports.main = async (event, context) => {
  // 标准的云函数处理逻辑
};
```

## ⚠️ 重要注意事项

### 1. 严格遵循开发规范
- ✅ **优先PC端User-Agent**：PC端数据更完整，移动端作为备用
- ✅ **正则表达式提取JSON**：不要硬编码数据位置
- ✅ **标准返回格式**：确保前端和结果页正常工作

### 2. 调试代码管理
- ✅ **开发阶段**：可以生成HTML、JSON调试文件
- ✅ **详细日志**：帮助理解数据结构和定位问题
- ❌ **生产环境**：必须删除所有调试文件生成和详细日志

### 3. 兼容性保证
- ❌ **不能影响现有平台**：新模块独立运行
- ❌ **不能修改前端代码**：统一解析器架构保证
- ❌ **不能改变数据格式**：结果页面兼容性要求

### 4. 代码质量
- ✅ **错误处理完整**：网络请求、JSON解析、数据提取
- ✅ **日志信息适度**：保留关键信息，删除冗余日志
- ✅ **代码注释清晰**：便于后续维护和理解

## 🚀 开发效率提升

### 快速开始模板

1. **复制现有解析器目录结构**
2. **修改平台特定的URL模式和JSON提取逻辑**
3. **在unified-parser中添加配置**
4. **测试和调试**
5. **清理调试代码，完成开发**

### 测试流程

1. **本地调试**：使用调试文件了解数据结构
2. **云函数测试**：部署到云端测试实际效果
3. **集成测试**：通过统一解析器测试完整流程
4. **前端验证**：确认结果页面正常显示

## 🎯 成功标准

### 开发完成的标志
- ✅ 能够成功解析目标平台的分享链接
- ✅ 返回标准格式的数据，结果页面正常显示
- ✅ 通过统一解析器的集成测试
- ✅ 删除所有调试代码和文件生成逻辑
- ✅ 前端无需任何修改即可支持新平台

### 质量检查清单
- [ ] 优先使用PC端User-Agent，移动端作为备用
- [ ] 通过正则表达式提取JSON数据  
- [ ] 返回标准数据格式
- [ ] 完整的错误处理机制
- [ ] 删除所有调试代码
- [ ] 在unified-parser中正确配置
- [ ] 不影响现有平台功能
- [ ] 前端自动支持新平台

## 📞 技术支持

如遇到开发问题，可以：
1. 参考现有解析器的架构设计
2. 查看统一解析器的配置方式
3. 检查数据格式是否符合标准要求

**记住：新平台开发的目标是实现"零前端修改"的平台扩展！** 🚀