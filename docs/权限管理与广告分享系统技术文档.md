# MarkEraser 权限管理与广告分享系统技术文档

## 📋 文档概述

本文档详细介绍 MarkEraser 项目中的统一权限管理系统，包括广告框架、分享机制和24小时权限逻辑。

**版本**: v2.0.0-stable  
**更新时间**: 2025年01月20日  
**适用范围**: 开发者、运维人员、产品经理

## 🎯 权限系统核心逻辑

### 24小时权限机制

用户通过以下任一方式可获得**24小时无限权限**：
1. **观看广告** - 看完广告后获得权限
2. **分享小程序** - 分享给好友后获得权限

**权限特点**：
- ✅ **任选其一** - 看广告或分享，任选一种方式即可
- ✅ **全局有效** - 获得权限后，所有操作（保存、复制、获取文案等）都无需再次验证
- ✅ **24小时有效** - 权限持续24小时，过期后需重新获取
- ✅ **自动检查** - 系统自动检查权限状态，用户无感知

### 权限获取流程

```
用户操作 → 权限检查 → 有24小时权限？
                    ├─ 是 → 直接执行操作
                    └─ 否 → 显示权限获取弹窗
                           ├─ 观看广告 → 获得24小时权限 → 执行操作
                           ├─ 分享小程序 → 获得24小时权限 → 执行操作
                           └─ 取消 → 停止操作
```

---

## 🏗️ 统一权限管理架构

### 1. 核心设计理念

MarkEraser 采用**统一权限管理系统**，实现以下目标：
- **统一入口**: 所有操作通过 `checkPermission()` 统一检查权限
- **多种方式**: 支持广告、分享等多种权限获取方式
- **防遗漏**: 自动覆盖所有需要权限的操作，防止遗漏
- **可扩展**: 支持未来添加会员、订阅等新权限类型

### 2. 权限管理架构图

```
┌─────────────────────────────────────────────────────────────┐
│                  统一权限管理系统                              │
├─────────────────────────────────────────────────────────────┤
│  📱 用户操作层                                                │
│  ├── 保存操作 (saveToAlbum, getCover...)                     │
│  ├── 复制操作 (getText, copyLink...)                         │
│  └── Live Photo操作 (copyLivePhotoUrl...)                    │
├─────────────────────────────────────────────────────────────┤
│  🔐 权限检查层 (pages/result/index.vue)                       │
│  ├── checkOperationPermission() - 统一权限检查入口            │
│  ├── 广告权限检查 (adManager)                                 │
│  └── 分享权限检查 (shareManager)                              │
├─────────────────────────────────────────────────────────────┤
│  ⚙️ 权限实现层                                                │
│  ├── 广告权限 (ad-config.js)                                 │
│  │   ├── 24小时权限检查                                       │
│  │   ├── 广告弹窗显示                                         │
│  │   └── 权限设置                                             │
│  └── 分享权限 (share-config.js)                              │
│      ├── 强制分享检查                                         │
│      ├── 分享弹窗显示                                         │
│      └── 权限设置                                             │
├─────────────────────────────────────────────────────────────┤
│  💾 存储层                                                    │
│  └── unlimited_access_time - 24小时权限时间戳                 │
└─────────────────────────────────────────────────────────────┘
```

### 3. 核心文件结构

```
components/
├── ad-config.js              # 广告配置和权限检查
├── share-config.js           # 分享配置和权限检查
├── operation-ad/             # 操作广告弹窗组件
│   └── operation-ad.vue
└── share-dialog/             # 分享弹窗组件
    └── share-dialog.vue

pages/result/
└── index.vue                 # 包含统一权限检查方法
```

---

## ⚙️ 配置说明

### 1. 广告配置 (ad-config.js)

#### 全局开关
```javascript
globalEnabled: true,  // 广告功能总开关
```

#### 操作前广告配置
```javascript
operationAd: {
  enabled: true,                        // 启用操作前广告
  showBeforeActions: {
    saveToAlbum: true,                  // 保存到相册前
    getText: true,                      // 获取文案前
    getCover: true,                     // 获取封面前
    copyLink: true,                     // 复制链接前
    copyLivePhotoUrl: true,             // 复制Live Photo链接前
  }
}
```

### 2. 分享配置 (share-config.js)

#### 全局开关
```javascript
globalEnabled: true,  // 分享功能总开关
testMode: true,       // 测试模式（发布前改为false）
```

#### 强制分享配置
```javascript
forceShare: {
  enabled: true,                        // 启用强制分享
  requireShareForActions: [
    'saveToAlbum',                      // 保存到相册
    'getText',                          // 获取文案
    'getCover',                         // 获取封面
    'copyLink',                         // 复制链接
    'copyLivePhotoUrl'                  // 复制Live Photo链接
  ]
}
```

---

## 🔧 开发者使用指南

### 1. 为新操作添加权限检查

**统一权限检查方法** - 所有操作都使用这个方法：

```javascript
// 在结果页面的方法中添加权限检查
async myNewOperation() {
  // 🔐 统一权限检查
  const allowed = await this.checkOperationPermission('myNewAction')
  if (!allowed) return

  // 执行具体操作
  // ...
}
```

**核心权限检查方法** - 位于 `pages/result/index.vue` 的 methods 中：

```javascript
// 🔐 统一权限检查方法 - 简单可靠
async checkOperationPermission(actionType) {
  console.log(`[权限检查] 开始检查: ${actionType}`)

  // 1. 检查24小时权限
  if (adManager.hasUnlimitedAccess()) {
    console.log(`[权限检查] 有24小时权限，允许操作`)
    return true
  }

  // 2. 显示广告（如果广告开启）
  if (adManager.config.globalEnabled && adManager.shouldShowAd('operation', actionType)) {
    console.log(`[权限检查] 显示广告`)
    const adResult = await adManager.showOperationAd(actionType)
    if (!adResult) {
      console.log(`[权限检查] 用户取消广告`)
      return false
    }
    console.log(`[权限检查] 广告完成`)
  }

  // 3. 检查分享权限（如果分享开启且需要分享）
  if (shareManager.config.globalEnabled && shareManager.shouldForceShare(actionType)) {
    const sharePermission = shareManager.checkActionPermission(actionType)
    if (!sharePermission.allowed) {
      console.log(`[权限检查] 需要分享权限`)
      return new Promise((resolve) => {
        shareManager.showForceShareDialog(actionType, (result) => {
          if (result.allowed) {
            console.log(`[权限检查] 分享成功`)
            resolve(true)
          } else {
            console.log(`[权限检查] 用户取消分享`)
            resolve(false)
          }
        })
      })
    }
  }

  console.log(`[权限检查] 权限检查通过`)
  return true
}
```

### 2. 配置广告显示

```javascript
// 在 ad-config.js 中添加
showBeforeActions: {
  myNewAction: true,  // 新操作前显示广告
}
```

### 3. 配置强制分享

```javascript
// 在 share-config.js 中添加
requireShareForActions: [
  'myNewAction'  // 新操作需要强制分享
]
```

### 4. 已实现的操作类型

当前已支持的操作类型：
- `saveToAlbum` - 保存到相册
- `getText` - 获取文案
- `getCover` - 获取封面
- `copyLink` - 复制链接
- `copyLivePhotoUrl` - 复制Live Photo链接

---

## 🚀 部署配置

### 测试环境
```javascript
// ad-config.js
globalEnabled: true,

// share-config.js
globalEnabled: true,
testMode: true,  // 测试模式，模拟分享成功
```

### 生产环境
```javascript
// ad-config.js
globalEnabled: true,

// share-config.js
globalEnabled: true,
testMode: false,  // 生产模式，使用真实微信分享
```

---

## 📊 权限状态监控

### 检查权限状态
```javascript
// 检查是否有24小时权限
const hasAccess = adManager.hasUnlimitedAccess()

// 获取剩余权限时间
const remainingTime = adManager.getRemainingAccessTime()

// 调试权限状态（在结果页面中）
console.log('权限检查方法可用:', typeof this.checkOperationPermission)
```

### 权限设置
```javascript
// 手动设置24小时权限（测试用）
const now = Date.now()
uni.setStorageSync('unlimited_access_time', now)

// 清除权限（测试用）
uni.removeStorageSync('unlimited_access_time')
```

---

## 🔍 故障排查

### 常见问题

1. **权限检查不生效**
   - 检查 `globalEnabled` 是否为 `true`
   - 检查操作类型是否在 `showBeforeActions` 中配置

2. **分享后仍要求权限**
   - 检查 `testMode` 配置
   - 检查分享成功回调是否正确执行

3. **权限过期时间不对**
   - 检查 `unlimited_access_time` 存储值
   - 检查系统时间是否正确

### 调试方法
```javascript
// 查看权限状态
console.log('权限时间戳:', uni.getStorageSync('unlimited_access_time'))
console.log('当前时间:', Date.now())
console.log('是否有权限:', adManager.hasUnlimitedAccess())

// 查看配置状态
console.log('广告配置:', adManager.config)
console.log('分享配置:', shareManager.config)
```

---

## 🎮 用户体验设计

### 权限获取弹窗设计

当用户没有24小时权限时，系统会显示统一的权限获取弹窗：

```
┌─────────────────────────────────────┐
│           需要权限才能继续             │
├─────────────────────────────────────┤
│  为了维持服务运营，请选择以下方式之一：  │
│                                     │
│  🎬 [观看广告]     📤 [分享小程序]     │
│                                     │
│  • 获得24小时无限权限                 │
│  • 所有功能免费使用                   │
│                                     │
│              [取消]                  │
└─────────────────────────────────────┘
```

### 权限状态提示

```javascript
// 权限剩余时间提示
if (remainingTime > 0) {
  const hours = Math.ceil(remainingTime / (60 * 60 * 1000))
  console.log(`剩余权限时间: ${hours}小时`)
}
```

---

## 🔒 安全机制

### 1. 防刷机制

虽然当前版本已简化，但保留了基础的防刷框架：

```javascript
// share-config.js 中的防刷配置（已禁用）
antiAbuse: {
  enabled: false,                       // 已禁用防刷机制
  minShareInterval: 0,                  // 无分享间隔限制
  maxSharesPerHour: 999,                // 无每小时分享次数限制
}
```

### 2. 权限验证

```javascript
// 权限时间戳验证
hasUnlimitedAccess() {
  const accessTime = uni.getStorageSync('unlimited_access_time')
  if (!accessTime) return false

  const now = Date.now()
  const duration = 24 * 60 * 60 * 1000  // 24小时

  return (now - accessTime) < duration
}
```

### 3. 数据完整性

- **本地存储**: 使用 `uni.getStorageSync/setStorageSync` 确保数据持久化
- **时间戳验证**: 严格验证权限时间戳的有效性
- **容错处理**: 异常情况下的降级处理

---

## 📈 性能优化

### 1. 权限检查优化

```javascript
// 缓存权限状态，避免重复计算
let cachedPermissionStatus = null
let lastCheckTime = 0

function getCachedPermissionStatus() {
  const now = Date.now()
  if (cachedPermissionStatus && (now - lastCheckTime) < 60000) {
    return cachedPermissionStatus
  }

  cachedPermissionStatus = adManager.hasUnlimitedAccess()
  lastCheckTime = now
  return cachedPermissionStatus
}
```

### 2. 事件监听优化

```javascript
// 避免重复监听事件
let eventListenersInitialized = false

function initEventListeners() {
  if (eventListenersInitialized) return

  uni.$on('showOperationAd', handleOperationAd)
  uni.$on('showShareDialog', handleShareDialog)

  eventListenersInitialized = true
}
```

---

## 📝 更新日志

### v2.0.0 (2025-01-20)
- ✅ 实现统一权限管理系统
- ✅ 简化分享功能，去除复杂限制
- ✅ 修复Live Photo权限控制遗漏
- ✅ 优化权限检查逻辑，防止重复验证

### v1.8.0 (2025-01-19)
- ✅ 完善强制分享功能
- ✅ 修复混合保存场景权限检查
- ✅ 优化广告显示时机

### v1.7.0 (2025-01-18)
- ✅ 实现24小时权限机制
- ✅ 添加广告框架
- ✅ 实现分享功能

---

## 🤝 技术支持

### 相关文件
- `pages/result/index.vue` - 包含统一权限检查方法
- `components/ad-config.js` - 广告配置管理
- `components/share-config.js` - 分享配置管理
- `开发进度记录.md` - 详细开发记录

### 联系方式
如有技术问题，请查看开发进度记录或联系开发团队。
