'use strict';

/**
 * 微视视频解析器
 * 支持微视视频链接解析和去水印处理
 */

// 全局调试配置
let DEBUG = false;

exports.main = async (event, context) => {
  const { link, debug = false } = event || {};
  DEBUG = !!debug;

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    console.log('🔵 开始解析微视链接:', link);
    
    // 清理链接
    const cleanedLink = cleanWeishiUrl(link);
    console.log('🔄 清理后的链接:', cleanedLink);

    // 尝试解析微视视频内容
    const result = await parseWeishiContent(cleanedLink);
    
    if (result.success) {
      // 总是输出解析成功的关键信息
      console.log('🎉 微视解析成功!');
      console.log('📹 视频信息:', {
        标题: result.data.title,
        作者: result.data.author,
        时长: Math.round(result.data.duration / 1000) + '秒',
        尺寸: `${result.data.width}x${result.data.height}`,
        点赞: result.data.likeCount,
        播放: result.data.playCount
      });
      console.log('🔗 媒体链接:', {
        视频URL: result.data.videoUrl.substring(0, 100) + '...',
        封面URL: result.data.coverUrl.substring(0, 100) + '...'
      });
      
      return {
        success: true,
        data: result.data,
        timestamp: new Date().toISOString(),
        version: '微视解析器 v1.0'
      };
    } else {
      console.error('❌ 微视解析失败:', result.message);
      return result;
    }

  } catch (error) {
    console.error('微视解析器异常:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: error.toString()
    };
  }
};

/**
 * 清理微视链接
 */
function cleanWeishiUrl(url) {
  if (!url) return url;
  
  let cleaned = url.trim();
  
  // 移除不必要的参数
  cleaned = cleaned.replace(/[?&]from=.*?(?=[?&]|$)/, '');
  cleaned = cleaned.replace(/[?&]type=.*?(?=[?&]|$)/, '');
  cleaned = cleaned.replace(/[?&]source=.*?(?=[?&]|$)/, '');
  
  return cleaned;
}

/**
 * 解析微视视频内容
 */
async function parseWeishiContent(url) {
  try {
    if (DEBUG) console.log('开始解析微视链接:', url);

    // 第一步：获取HTML页面
    const htmlResult = await getWeishiHtml(url);
    if (!htmlResult.success) {
      return { success: false, message: '获取页面失败: ' + htmlResult.message };
    }

    if (DEBUG) console.log('✅ HTML获取成功，开始提取JSON数据');

    // 第二步：从HTML中提取JSON数据
    const jsonData = extractJsonFromHtml(htmlResult.html);
    if (!jsonData || Object.keys(jsonData).length === 0) {
      return { success: false, message: '未能从页面中提取到JSON数据' };
    }

    if (DEBUG) console.log('✅ JSON数据提取成功，开始解析视频信息');

    // 第三步：从JSON中解析视频信息
    const videoData = parseVideoFromJson(jsonData);
    if (!videoData.success) {
      return { success: false, message: '解析视频信息失败: ' + videoData.message };
    }

    return {
      success: true,
      data: videoData.data
    };

  } catch (error) {
    throw error;
  }
}

/**
 * 获取微视页面HTML - 使用移动端User-Agent（最佳方法，包含完整JSON数据）
 */
async function getWeishiHtml(url) {
  try {
    if (DEBUG) console.log('🌐 开始获取微视页面HTML（移动端User-Agent）');

    // 使用移动端User-Agent - 这是获取JSON数据最可靠的方法
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 20000,
      dataType: 'text',
      followRedirect: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });

    if (response.status === 200 && response.data) {
      if (DEBUG) console.log('✅ 移动端User-Agent方法成功获取HTML');
      return { success: true, html: response.data };
    }

    return { success: false, message: `HTTP ${response.status}` };

  } catch (error) {
    return { success: false, message: error.message };
  }
}

// 已删除其他两种获取HTML的方法，只保留最佳的移动端User-Agent方法

/**
 * 从HTML中提取JSON数据
 */
function extractJsonFromHtml(html) {
  if (DEBUG) console.log('🔍 开始从HTML中提取JSON数据');

  try {
    // 查找 window.Vise.initState 数据
    const pattern = /window\.Vise\.initState\s*=\s*({[\s\S]*?});\s*}\s*catch/;
    const match = html.match(pattern);
    
    if (match && match[1]) {
      const jsonStr = match[1];
      if (DEBUG) console.log('找到JSON数据，长度:', jsonStr.length);
      
      try {
        const jsonData = JSON.parse(jsonStr);
        if (DEBUG) console.log('✅ JSON解析成功');
        
        // 打印完整的JSON数据到控制台
        console.log('📄 ===== 完整的微视JSON数据 =====');
        console.log(jsonData);
        console.log('📄 ===== JSON数据结束 =====');
        
        return jsonData;
      } catch (parseError) {
        if (DEBUG) console.log('❌ JSON解析失败:', parseError.message);
        return {};
      }
    } else {
      if (DEBUG) console.log('❌ 未找到 window.Vise.initState 数据');
      return {};
    }
  } catch (error) {
    if (DEBUG) console.log('❌ 提取JSON失败:', error.message);
    return {};
  }
}

/**
 * 从JSON数据中解析视频信息
 */
function parseVideoFromJson(jsonData) {
  if (DEBUG) console.log('🎬 开始从JSON数据中解析视频信息');

  try {
    if (!jsonData.feedsList || !Array.isArray(jsonData.feedsList) || jsonData.feedsList.length === 0) {
      throw new Error('未找到 feedsList 数组或数组为空');
    }
    
    const feedData = jsonData.feedsList[0]; // 取第一个视频
    if (DEBUG) console.log('📋 视频数据键:', Object.keys(feedData).slice(0, 10));
    
    // 提取基本信息
    const videoInfo = {
      // 基本信息
      id: feedData.id || '',
      title: cleanVideoText(feedData.feedDesc || ''),
      author: cleanVideoText(feedData.poster?.nick || ''),
      authorId: feedData.posterId || '',
      avatar: upgradeToHttps(feedData.poster?.avatar || ''),
      
      // 视频信息
      videoUrl: upgradeToHttps(feedData.videoUrl || ''),
      coverUrl: getBestCoverUrl(feedData),
      duration: feedData.video?.duration || 0,
      width: feedData.video?.width || 0,
      height: feedData.video?.height || 0,
      
      // 统计信息
      likeCount: feedData.dingCount || 0,
      commentCount: feedData.totalCommentNum || 0,
      playCount: feedData.playNum || 0,
      
      // 音乐信息
      musicDesc: cleanVideoText(feedData.materialDesc || ''),
      musicId: feedData.musicId || '',
      
      // 其他
      fileId: feedData.video?.fileId || '',
      images: (feedData.images || []).map(img => ({
        url: upgradeToHttps(img.url || ''),
        width: img.width || 0,
        height: img.height || 0
      })),
      
      // 视频规格URLs（用于选择最佳清晰度）
      videoSpecUrls: feedData.videoSpecUrls || {},
      
      // 解析方法标识
      method: 'weishi_html_parser',
      hasVideo: true,
      type: 'video'
    };
    
    // 选择最佳视频URL（优先选择较小的H.265文件）
    const bestVideoUrl = selectBestVideoUrl(feedData.videoSpecUrls);
    if (bestVideoUrl) {
      videoInfo.videoUrl = upgradeToHttps(bestVideoUrl);
    }
    
    if (DEBUG) {
      console.log('✅ 视频解析成功:', {
        id: videoInfo.id,
        title: videoInfo.title.substring(0, 30) + '...',
        author: videoInfo.author,
        duration: videoInfo.duration,
        size: `${videoInfo.width}x${videoInfo.height}`
      });
    }
    
    return {
      success: true,
      data: videoInfo
    };
    
  } catch (error) {
    if (DEBUG) console.log('❌ 视频解析失败:', error.message);
    return { 
      success: false, 
      message: error.message 
    };
  }
}

/**
 * 获取最佳封面URL - 优先选择高清图片
 */
function getBestCoverUrl(feedData) {
  // 优先使用 images 数组中的高清图片
  if (feedData.images && Array.isArray(feedData.images) && feedData.images.length > 0) {
    // 按宽度排序，选择最大的
    const sortedImages = feedData.images.sort((a, b) => (b.width || 0) - (a.width || 0));
    const bestImage = sortedImages[0];
    if (bestImage && bestImage.url) {
      return upgradeToHttps(bestImage.url);
    }
  }
  
  // 回退到默认的 videoCover
  return upgradeToHttps(feedData.videoCover || '');
}

/**
 * 选择最佳视频URL - 优先选择微信播放器兼容的H.264编码，并选择最高清版本
 */
function selectBestVideoUrl(videoSpecUrls) {
  if (!videoSpecUrls || Object.keys(videoSpecUrls).length === 0) {
    return '';
  }
  
  // 优先选择H.264编码（兼容性最好，微信播放器支持）
  const specs = Object.entries(videoSpecUrls);
  
  // 先找H.264编码的（videoCoding: 1）
  const h264Specs = specs.filter(([spec, info]) => info.videoCoding === 1);
  if (h264Specs.length > 0) {
    // H.264编码中选择最高清的（按推荐规格 > 分辨率 > 文件大小排序）
    h264Specs.sort((a, b) => {
      const aSpec = a[1];
      const bSpec = b[1];
      
      // 优先选择推荐规格
      if (aSpec.recommendSpec > 0 && bSpec.recommendSpec === 0) return -1;
      if (bSpec.recommendSpec > 0 && aSpec.recommendSpec === 0) return 1;
      
      // 按分辨率排序（宽度 * 高度）
      const aResolution = (aSpec.width || 0) * (aSpec.height || 0);
      const bResolution = (bSpec.width || 0) * (bSpec.height || 0);
      if (aResolution !== bResolution) return bResolution - aResolution;
      
      // 最后按文件大小排序（大文件通常质量更好）
      return (bSpec.size || 0) - (aSpec.size || 0);
    });
    
    console.log('🎯 选择H.264最高清版本:', {
      选中规格: h264Specs[0][0],
      分辨率: `${h264Specs[0][1].width}x${h264Specs[0][1].height}`,
      文件大小: `${Math.round(h264Specs[0][1].size / 1024 / 1024 * 10) / 10}MB`,
      推荐规格: h264Specs[0][1].recommendSpec,
      编码格式: 'H.264'
    });
    
    return h264Specs[0][1].url;
  }
  
  // 如果没有H.264，才选择其他编码（向后兼容）
  console.log('⚠️ 未找到H.264编码，选择其他格式');
  return specs[0][1].url;
}

/**
 * 清理视频文本内容
 */
function cleanVideoText(text) {
  if (!text || typeof text !== 'string') return '';
  
  return text
    .replace(/\s+/g, ' ') // 多个空格合并为一个
    .trim();
}

/**
 * 升级HTTP链接为HTTPS
 */
function upgradeToHttps(url) {
  if (!url || typeof url !== 'string') return '';
  
  if (url.startsWith('//')) {
    return `https:${url}`;
  }
  if (url.startsWith('http://')) {
    return url.replace(/^http:\/\//i, 'https://');
  }
  
  return url;
}