'use strict';

/**
 * B 站解析器（基础版）
 * 说明：B 站直链播放通常需要携带特殊请求头，H5 内联播放大概率失败。
 * 本基础版仅做短链还原与基础页面信息提取，返回友好提示，避免 unified-parser 报错。
 * 后续可扩展为真实可下载地址的获取（需要更复杂的接口与签名处理）。
 */



const DESKTOP_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Referer': 'https://www.bilibili.com/'
};



function buildCookieHeaderFromEnv() {
  try {
    const sess = process.env.BILI_SESSDATA || '';
    const buvid3 = process.env.BILI_BUVID3 || '';
    const biliJct = process.env.BILI_JCT || process.env.BILI_BILI_JCT || '';
    const parts = [];
    if (sess) parts.push(`SESSDATA=${sess}`);
    if (buvid3) parts.push(`buvid3=${buvid3}`);
    if (biliJct) parts.push(`bili_jct=${biliJct}`);
    const cookie = parts.join('; ');
    return cookie ? { Cookie: cookie } : {};
  } catch (_) {
    return {};
  }
}

exports.main = async (event, context) => {
  const { link, debug = false } = event || {};

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    // 1) 处理 b23.tv 短链 → 还原真实页面地址
    const resolvedUrl = await resolveShortLink(link);

    // 2) 仅抓取 PC 端页面，提取基础信息和初始化 JSON
    const pcPage = await fetchPageAndExtract(resolvedUrl, DESKTOP_HEADERS);

    // 使用 PC 版本数据
    const { pageInfo, initialState, initialStateRaw, html } = pcPage;

    // 额外兜底：从初始化 JSON 中再尝试提取关键字段
    const fallbackFromState = extractFromInitialState(initialState);
    const isDynamicType = initialState?.detail?.type === 0; // 只有动态才有Live Photo
    const title = fallbackFromState.isDynamic ? 
      (fallbackFromState.title || pageInfo.title || '') : 
      (pageInfo.title || fallbackFromState.title || '');
    const author = pageInfo.author || fallbackFromState.author || '';
    // 优先使用JSON数据中的封面（格式更标准），HTML meta作为备选
    const cover = fallbackFromState.cover || pageInfo.cover || null;

    const bvid = fallbackFromState.bvid || '';
    const cid = fallbackFromState.cid || '';
    const desc = fallbackFromState.desc || '';

    // 3) 尝试获取可下载直链（用于保存到相册）
    let downloadUrl = '';
    let videoUrls = [];
    let videoSource = '';
    if (bvid && cid) {
      const play = await fetchPlayUrl(bvid, cid);
      downloadUrl = play.primaryUrl || '';
      videoUrls = play.allUrls || [];
      videoSource = play.source || '';
      if (videoSource) {
        console.log(`🎬 视频链接来源: ${videoSource}`);
      }
    }

    // 若拿不到直链，则回退为页面 URL
    const playableUrl = downloadUrl || resolvedUrl;



    // 5) 判断内容类型并处理标题
    let contentType = 'video'; // 默认视频类型
    let finalTitle = title;
    
    if (fallbackFromState.isDynamic) {
      // 动态内容：根据是否有图片决定类型
      if (fallbackFromState.images && fallbackFromState.images.length > 0) {
        contentType = 'image'; // 有图片的图文内容
      } else {
        contentType = 'text'; // 纯文本内容
        // 纯文本内容：只有在标题为空或无意义时才从正文截取
        if (!title || title === 'B站动态' || title === 'B站专栏' || title.trim().length === 0) {
          const content = desc || '';
          if (content.length > 10) {
            finalTitle = content.substring(0, 10) + '...';
          } else if (content.length > 0) {
            finalTitle = content;
          } else {
            finalTitle = 'B站动态';
          }
        }
        // 如果有有效标题，直接使用原标题
      }
    }

    // 6) 构造返回
    const data = {
      title: finalTitle,
      author,
      content: desc || '',
      coverUrl: cover,
      type: contentType,
      platform: 'bilibili',
      source: 'B站',
      originalUrl: link,

      // processedData 根据内容类型返回不同数据
      processedData: contentType === 'text' ? {
        // 纯文本内容：不需要图片或视频数据
        data: '', // 纯文本无需下载数据
        isUrl: false,
        type: 'text/plain',
        hasDirectUrl: false,
        raw: {
          isDynamic: true,
          contentType: 'text',
          initialStateExists: !!initialState,
          initialStateKeys: initialState ? Object.keys(initialState).slice(0, 20) : [],
          initialStateSize: initialStateRaw ? initialStateRaw.length : 0
        }
      } : contentType === 'image' ? {
        // 图文视频：返回图片数据
        data: fallbackFromState.images[0] || '', // 第一张图片作为主图
        isUrl: true,
        type: 'image/jpeg',
        imageUrls: fallbackFromState.images, // 所有图片URL数组
        livePhotoVideos: isDynamicType ? fallbackFromState.images.map((imageUrl, index) => {
          // 找到对应图片的live photo
          const livePhoto = fallbackFromState.livePhotos?.find(lp => lp.imageUrl === imageUrl);
          return livePhoto ? livePhoto.videoUrl : null;
        }) || [] : [], // 只有动态才有Live Photo，专栏只有静态图片
        // 添加videoUrls字段，只包含有效的视频URL，与小红书保持一致
        videoUrls: isDynamicType ? fallbackFromState.livePhotos?.map(lp => lp.videoUrl).filter(url => url && url.trim() !== '') || [] : [],
        // 添加hasLivePhoto字段，与小红书保持一致
        hasLivePhoto: isDynamicType ? fallbackFromState.livePhotos && fallbackFromState.livePhotos.length > 0 : false,
        hasDirectUrl: true,
        raw: {
          isDynamic: true,
          images: fallbackFromState.images,
          livePhotos: fallbackFromState.livePhotos || [],
          livePhotoVideos: isDynamicType ? fallbackFromState.livePhotos?.map(lp => lp.videoUrl) || [] : [],
          initialStateExists: !!initialState,
          initialStateKeys: initialState ? Object.keys(initialState).slice(0, 20) : [],
          initialStateSize: initialStateRaw ? initialStateRaw.length : 0
        }
      } : {
        // 普通视频：返回视频数据
        data: playableUrl,
        isUrl: true,
        type: 'video/mp4',
        // 可能不是直链，前端 video 标签播放失败将给出友好提示
        videoUrls,
        hasDirectUrl: !!downloadUrl,
        // 方便前端查看原始结构
        raw: {
          bvid,
          cid,
          videoSource, // 显示视频链接来源
          initialStateExists: !!initialState,
          initialStateKeys: initialState ? Object.keys(initialState).slice(0, 20) : [],
          initialStateSize: initialStateRaw ? initialStateRaw.length : 0
        }
      },

      note: downloadUrl
        ? '已获取到可下载直链。内联播放可能仍受限，建议直接保存到相册或复制链接。'
        : '提示：B站直链通常需要特殊请求头。若播放失败，请使用“保存到相册”或“复制链接”。',
      debug: debug ? {
        resolvedUrl,
        pageInfo,
        initialStateSample: sampleInitialState(initialState),
        initialStateRawPreview: initialStateRaw ? initialStateRaw.slice(0, 8000) : ''
      } : undefined
    };

    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'B站解析失败'
    };
  }
};

/**
 * 还原 b23.tv 等短链
 */
async function resolveShortLink(shareUrl) {
  try {
    const res = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,
      timeout: 15000,
      headers: DESKTOP_HEADERS
    });

    const location = res.headers.location || res.headers.Location;
    if (location && typeof location === 'string') {
      // B 站会多次跳转，这里仅处理第一跳；其余由前端/后续请求处理
      return location.startsWith('http') ? location : `https:${location}`;
    }
    return shareUrl;
  } catch (e) {
    return shareUrl;
  }
}

/**
 * 抓取页面并提取基础信息与 window.__INITIAL_STATE__
 */
async function fetchPageAndExtract(url, headers) {
  try {
    const res = await uniCloud.httpclient.request(url, {
      method: 'GET',
      followRedirect: true,
      timeout: 15000,
      dataType: 'text',
      headers: headers || DESKTOP_HEADERS
    });

    const html = typeof res.data === 'string' ? res.data : (res.data?.toString?.() || '');
    if (!html) return { pageInfo: {}, initialState: null, initialStateRaw: '', html: '' };

    const title = matchMetaContent(html, 'og:title') || matchTitle(html);
    const cover = matchMetaContent(html, 'og:image');
    const authorMeta = matchMetaContent(html, 'og:author') || '';
    
    console.log('📸 HTML meta封面:', cover);

    // 解析 window.__INITIAL_STATE__
    const { json: initialState, raw: initialStateRaw } = extractInitialStateJson(html);

    return {
      pageInfo: { title, cover, author: authorMeta },
      initialState,
      initialStateRaw,
      html
    };
  } catch (e) {
    return { pageInfo: {}, initialState: null, initialStateRaw: '', html: '' };
  }
}

function matchMetaContent(html, property) {
  const reg = new RegExp(`<meta[^>]+property=["']${escapeReg(property)}["'][^>]*content=["']([^"']+)["'][^>]*>`, 'i');
  const m = html.match(reg);
  return m ? decodeHtml(m[1]) : '';
}

function matchTitle(html) {
  const m = html.match(/<title>([^<]+)<\/title>/i);
  return m ? decodeHtml(m[1]) : '';
}

function escapeReg(str) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function decodeHtml(str) {
  try {
    return str
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");
  } catch (_) {
    return str;
  }
}

/**
 * 提取 window.__INITIAL_STATE__ JSON
 */
function extractInitialStateJson(html) {
  try {
    // 尝试多种正则表达式模式来匹配不同格式的 __INITIAL_STATE__
    const patterns = [
      /window\.__INITIAL_STATE__\s*=\s*(\{[\s\S]*?\})\s*;\s*(?:\(function|<\/script>)/,
      /window\.__INITIAL_STATE__\s*=\s*(\{[\s\S]*?\})\s*;/,
      /window\.__INITIAL_STATE__\s*=\s*(\{[\s\S]*?\})/,
      /__INITIAL_STATE__\s*=\s*(\{[\s\S]*?\})\s*;/
    ];
    
    for (const pattern of patterns) {
      const m = html.match(pattern);
      if (m) {
        const raw = m[1];
        // 有些页面末尾可能多一个分号，尝试安全解析
        const cleaned = raw.trim();
        try {
          const json = JSON.parse(cleaned);
          console.log('✅ 成功提取 __INITIAL_STATE__ JSON数据');
          return { json, raw: cleaned };
        } catch (parseError) {
          console.log('❌ JSON解析失败，尝试下一个模式:', parseError.message);
          continue;
        }
      }
    }
    
    console.log('❌ 未找到 __INITIAL_STATE__ 数据');
    return { json: null, raw: '' };
  } catch (e) {
    console.log('❌ extractInitialStateJson 异常:', e.message);
    return { json: null, raw: '' };
  }
}

/**
 * 从 __INITIAL_STATE__ 中提取关键信息
 */
function extractFromInitialState(state) {
  if (!state || typeof state !== 'object') return {};
  try {
    // 🔍 使用真正的类型标识字段：detail.type
    const detailType = state?.detail?.type;
    const isDynamic = detailType === 0;
    const isArticle = detailType === 1;
    
    if (isDynamic || isArticle) {
      console.log(`🎯 检测到B站${isDynamic ? '动态' : '专栏'}（图文内容）- detail.type: ${detailType}`);
      return extractDynamicData(state);
    }
    
    // 普通视频处理
    const videoData = state.videoData || state?.view?.data || state?.initArchive || null;
    const bvid = videoData?.bvid || state?.bvid || '';
    // cid 可能位于不同层
    const cid = videoData?.cid || state?.cid || (state?.videoInfo?.cid) || '';
    const title = videoData?.title || state?.h1Title || '';
    const desc = videoData?.desc || '';
    const cover = videoData?.pic || videoData?.cover || '';
    const author = videoData?.owner?.name || videoData?.author || state?.upData?.name || '';
    

    
    return { title, author, cover, bvid, cid, desc };
  } catch (_) {
    return {};
  }
}

/**
 * 从B站动态（图文视频）中提取数据
 */
function extractDynamicData(state) {
  try {
    const detail = state.detail;
    const modules = detail.modules || [];
    

    
    let title = '';
    let author = '';
    let cover = '';
    let desc = '';
    let images = [];
    let livePhotos = []; // 新增：live photo数组
    
    // 遍历模块提取信息
    modules.forEach((module, index) => {
      
      switch (module.module_type) {
        case 'MODULE_TYPE_TOP':
          // 大封面格式：图片在 module_top 中
          const topModule = module.module_top;
          if (topModule?.display?.album?.pics) {
            const pics = topModule.display.album.pics;
            pics.forEach((pic, picIndex) => {
              if (pic.url) {
                // 将B站图片的HTTP链接转换为HTTPS，解决iOS真机加载问题
                const imageUrl = pic.url.startsWith('http://') ? pic.url.replace('http://', 'https://') : pic.url;
                images.push(imageUrl);
                // 检查是否为live photo - 只有当live_url存在且不为空时才添加
                if (pic.live_url && pic.live_url.trim() !== '') {
                  const liveVideoUrl = pic.live_url.startsWith('http://') ? pic.live_url.replace('http://', 'https://') : pic.live_url;
                  livePhotos.push({
                    imageUrl: imageUrl,
                    videoUrl: liveVideoUrl,
                    index: picIndex
                  });
                }
              }
            });
          }
          break;
          
        case 'MODULE_TYPE_TITLE':
          title = module.module_title?.text || '';
          break;
          
        case 'MODULE_TYPE_AUTHOR':
          author = module.module_author?.name || '';
          break;
          
        case 'MODULE_TYPE_CONTENT':
          // 提取文本和图片（九宫格格式）
          const paragraphs = module.module_content?.paragraphs || [];
          paragraphs.forEach((para, pIndex) => {
            if (para.para_type === 1 && para.text) {
              // 文本段落
              const textNodes = para.text.nodes || [];
              const text = textNodes.map(node => node.word?.words || '').join('');
              desc += text + '\n';
            } else if (para.para_type === 2 && para.pic) {
              // 图片段落 - 普通图文动态的图片，不包含live photo
              const pics = para.pic.pics || [];
              pics.forEach(pic => {
                if (pic.url) {
                  // 将B站图片的HTTP链接转换为HTTPS，解决iOS真机加载问题
                  const imageUrl = pic.url.startsWith('http://') ? pic.url.replace('http://', 'https://') : pic.url;
                  images.push(imageUrl);
                  // 注意：九宫格格式的图片通常不包含live photo功能
                  // 只有大封面格式才可能有live photo
                }
              });
            }
          });
          break;
      }
    });
    
    // 选择第一张图片作为封面
    if (images.length > 0) {
      cover = images[0];
    }
    
    // 清理描述文本
    desc = desc.trim();
    
    return {
      title: title || (state?.detail?.type === 1 ? 'B站专栏' : 'B站动态'),
      author: author || '未知作者',
      cover,
      bvid: '', // 动态没有bvid
      cid: '', // 动态没有cid
      desc,
      images, // 新增：图片数组
      livePhotos, // 新增：live photo数组
      isDynamic: true // 新增：标识为动态
    };
  } catch (error) {
    console.error('❌ 动态数据提取失败:', error);
    return {
      title: 'B站动态',
      author: '未知作者',
      cover: '',
      bvid: '',
      cid: '',
      desc: '',
      images: [],
      isDynamic: true
    };
  }
}

function sampleInitialState(state) {
  if (!state || typeof state !== 'object') return null;
  const sample = {};
  
  // 基础字段
  ['videoData', 'bvid', 'cid', 'upData', 'view', 'readInfo', 'articleMeta', 'h1Title'].forEach((key) => {
    if (state[key] !== undefined) {
      sample[key] = typeof state[key] === 'object' ? Object.keys(state[key]).slice(0, 20) : state[key];
    }
  });
  
  return sample;
}

/**
 * 调 B 站播放接口，尝试拿到直链
 * 注：B 站对 UA/Referer/Origin 等有要求；此接口未签名场景下可能返回受限或空。
 */
async function fetchPlayUrl(bvid, cid) {
  try {
    // 仅使用PC端请求策略
    const candidates = [
      // PC - durl（mp4）
      {
        url: `https://api.bilibili.com/x/player/playurl?bvid=${encodeURIComponent(bvid)}&cid=${encodeURIComponent(cid)}&qn=80&fnval=0&fourk=1&fnver=0&platform=html5&type=mp4`,
        headers: { ...DESKTOP_HEADERS, ...buildCookieHeaderFromEnv(), Origin: 'https://www.bilibili.com' }
      },
      // PC - html5 mp4
      {
        url: `https://api.bilibili.com/x/player/playurl?bvid=${encodeURIComponent(bvid)}&cid=${encodeURIComponent(cid)}&qn=80&fnval=0&platform=html5&type=mp4&otype=json`,
        headers: { ...DESKTOP_HEADERS, ...buildCookieHeaderFromEnv(), Origin: 'https://www.bilibili.com' }
      }
    ];

    const collectUrls = (payload) => {
      const urls = [];
      if (!payload || payload.code !== 0 || !payload.data) return urls;
      // durl（mp4）
      if (Array.isArray(payload.data.durl) && payload.data.durl.length > 0) {
        for (const part of payload.data.durl) {
          if (part && part.url) urls.push(part.url);
          if (Array.isArray(part.backup_url)) urls.push(...part.backup_url);
        }
      }
      // dash（m4s）作为备选
      if (payload.data.dash && Array.isArray(payload.data.dash.video)) {
        for (const v of payload.data.dash.video) {
          if (v && v.baseUrl) urls.push(v.baseUrl);
          if (Array.isArray(v.backupUrl)) urls.push(...v.backupUrl);
        }
      }
      return urls;
    };

    for (let i = 0; i < candidates.length; i++) {
      const c = candidates[i];
      const requestType = 'PC端';
      const quality = '高清';
      
      try {
        const res = await uniCloud.httpclient.request(c.url, {
          method: 'GET',
          timeout: 15000,
          dataType: 'json',
          headers: c.headers
        });
        const payload = res.data || {};
        const urls = collectUrls(payload);
        const unique = Array.from(new Set(urls));
        // 优先返回 durl(mp4)。若仅有 dash，则也一并返回供前端复制下载
        if (unique.length > 0) {
          return { 
            primaryUrl: unique[0], 
            allUrls: unique,
            source: `${requestType}-${quality}`,
            candidateIndex: i
          };
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    return { primaryUrl: '', allUrls: [] };
  } catch (e) {
    return { primaryUrl: '', allUrls: [] };
  }
}

