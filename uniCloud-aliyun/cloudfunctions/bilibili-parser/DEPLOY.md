# B站解析器部署说明

## 部署步骤

### 1. 上传云函数
1. 在 HBuilderX 中打开项目
2. 展开 `uniCloud-aliyun/cloudfunctions/` 目录
3. 右键点击 `bilibili-parser` 文件夹
4. 选择 "上传并运行"

### 2. 验证部署
1. 打开 uniCloud 控制台
2. 进入云函数管理页面
3. 确认 `bilibili-parser` 函数已成功部署
4. 检查函数状态为"正常"

### 3. 测试功能
1. 访问测试页面：`pages/test/bilibili-test.vue`
2. 输入B站视频链接进行测试
3. 验证解析结果是否正确

## 配置检查

### 统一解析器配置
确保 `unified-parser/index.js` 中已添加B站配置：

```javascript
bilibili: {
  name: 'B站',
  parser: 'bilibili-parser',
  patterns: [
    /bilibili\.com/i,
    /b23\.tv/i,
    /b23\.com/i
  ],
  defaultOptions: {
    forceRemoveWatermark: false,
    debug: false
  }
}
```

### 前端自动支持
由于使用了统一解析器架构，前端无需任何修改即可支持B站解析。

## 常见问题

### 1. 云函数上传失败
- 检查网络连接
- 确认 uniCloud 配置正确
- 检查函数代码语法错误

### 2. 解析失败
- 检查B站链接格式是否正确
- 确认视频为公开可访问
- 查看云函数日志排查问题

### 3. 视频链接无效
- B站视频可能已下架或删除
- 检查是否为会员专享内容
- 确认链接格式支持

## 监控和维护

### 1. 日志监控
- 定期检查云函数运行日志
- 关注错误率和响应时间
- 监控网络请求成功率

### 2. 性能优化
- 根据使用情况调整超时时间
- 优化请求头配置
- 考虑添加缓存机制

### 3. 合规检查
- 确保使用符合B站用户协议
- 定期检查解析功能是否正常
- 关注B站API变化

## 技术支持

如遇到部署问题，请：
1. 检查云函数日志
2. 确认配置是否正确
3. 联系开发团队获取支持


