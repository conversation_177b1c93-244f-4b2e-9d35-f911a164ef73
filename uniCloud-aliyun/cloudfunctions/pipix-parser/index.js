'use strict';

/**
 * 皮皮虾视频和图文解析器
 * 支持纯视频和图文内容解析
 */

// 全局调试配置
let DEBUG = false;

exports.main = async (event, context) => {
  const { link, debug = false } = event || {};
  DEBUG = !!debug;

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    console.log('🔵 开始解析皮皮虾链接:', link);
    
    // 清理链接
    const cleanedLink = cleanPipixUrl(link);
    console.log('🔄 清理后的链接:', cleanedLink);

    // 尝试解析皮皮虾内容
    const result = await parsePipixContent(cleanedLink);
    
    if (result.success) {
      // 总是输出解析成功的关键信息
      console.log('🎉 皮皮虾解析成功!');
      console.log('📄 内容信息:', {
        标题: result.data.title?.substring(0, 30) + '...',
        作者: result.data.author,
        类型: result.data.type,
        内容数量: result.data.type === 'video' ? '1个视频' : 
                  result.data.type === 'image' ? `${result.data.images?.length || 0}张图片` : 
                  result.data.type === 'text' ? '纯文本内容' : '未知'
      });
      
      return result;
    } else {
      console.log('❌ 皮皮虾解析失败:', result.message);
      return result;
    }
    
  } catch (error) {
    console.error('💥 皮皮虾解析异常:', error.message);
    return { success: false, message: error.message };
  }
};

/**
 * 清理皮皮虾链接
 */
function cleanPipixUrl(url) {
  if (!url || typeof url !== 'string') return '';
  
  try {
    // 移除多余参数，保留核心链接
    url = url.trim();
    
    // 处理短链接重定向后的长链接
    if (url.includes('h5.pipix.com')) {
      return url.split('?')[0]; // 移除查询参数
    }
    
    return url;
  } catch (error) {
    return url;
  }
}

/**
 * 解析皮皮虾内容
 */
async function parsePipixContent(url) {
  try {
    if (DEBUG) console.log('🌐 开始获取皮皮虾页面HTML');

    // 获取HTML内容（使用移动端User-Agent）
    const htmlResult = await getPipixHtml(url);
    if (!htmlResult.success) {
      return { success: false, message: htmlResult.message };
    }

    if (DEBUG) console.log('✅ HTML获取成功，长度:', htmlResult.html.length);

    // 从HTML中提取JSON数据
    const jsonData = extractJsonFromHtml(htmlResult.html);
    if (!jsonData || !jsonData.ppxItemDetail) {
      return { success: false, message: '未找到有效的皮皮虾数据' };
    }

    if (DEBUG) {
      console.log('📄 ===== 完整的皮皮虾JSON数据 =====');
      console.log(jsonData);
      console.log('📄 ===== JSON数据结束 =====');
    }

    // 解析内容信息
    let itemData = null;
    
    // 获取ppxCellComment
    if (jsonData.ppxCellComment && jsonData.ppxCellComment.cell_comments && jsonData.ppxCellComment.cell_comments[0]) {
      itemData = jsonData.ppxCellComment.cell_comments[0].comment_info.item;
    }
    
    // 如果ppxCellComment为空，尝试从ppxItemDetail中获取
    if (!itemData && jsonData.ppxItemDetail && jsonData.ppxItemDetail.item) {
      if (DEBUG) console.log('⚠️ ppxCellComment为空，使用ppxItemDetail.item作为fallback');
      itemData = jsonData.ppxItemDetail.item;
    }
    
    if (!itemData) {
      if (DEBUG) console.log('❌ 未找到itemData，检查JSON结构:', Object.keys(jsonData));
      return { success: false, message: '皮皮虾内容数据为空' };
    }

    // 根据内容类型进行解析
    if (itemData.item_type === 2) {
      // 视频类型
      return parseVideoContent(itemData);
    } else if (itemData.item_type === 1) {
      // 图文类型
      return parseImageContent(itemData);
    } else {
      return { success: false, message: `不支持的内容类型: ${itemData.item_type}` };
    }

  } catch (error) {
    return { success: false, message: error.message };
  }
}

/**
 * 获取皮皮虾页面HTML
 */
async function getPipixHtml(url) {
  try {
    if (DEBUG) console.log('🌐 开始获取皮皮虾页面HTML');

    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 20000,
      dataType: 'text',
      followRedirect: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });

    if (response.status === 200 && response.data) {
      return { success: true, html: response.data };
    }

    return { success: false, message: `HTTP ${response.status}` };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

/**
 * 从HTML中提取JSON数据
 */
function extractJsonFromHtml(html) {
  if (DEBUG) console.log('🔍 开始从HTML中提取JSON数据');

  try {
    // 查找script标签中的URL编码数据
    const scriptPattern = /<script[^>]*>([^<]*)<\/script>/g;
    let match;
    
    while ((match = scriptPattern.exec(html)) !== null) {
      const scriptContent = match[1].trim();
      
      if (scriptContent.includes('%') && scriptContent.length > 1000) {
        if (DEBUG) console.log('✅ 找到大型URL编码数据');
        
        try {
          // URL解码
          const decoded = decodeURIComponent(scriptContent);
          
          // JSON解析
          const jsonData = JSON.parse(decoded);
          if (DEBUG) console.log('✅ JSON解析成功');
          
          return jsonData;
        } catch (parseError) {
          if (DEBUG) console.log('❌ 数据解析失败:', parseError.message);
        }
      }
    }
    
    if (DEBUG) console.log('❌ 未找到有效的JSON数据');
    return null;
    
  } catch (error) {
    if (DEBUG) console.log('❌ 提取JSON失败:', error.message);
    return null;
  }
}

/**
 * 解析视频内容
 */
function parseVideoContent(itemData) {
  try {
    if (DEBUG) console.log('🎬 开始解析视频内容');

    const videoData = itemData.video;
    if (!videoData) {
      return { success: false, message: '视频数据不存在' };
    }

    // 获取最佳视频URL
    const videoUrl = getBestVideoUrl(videoData);
    if (!videoUrl) {
      return { success: false, message: '未找到有效的视频URL' };
    }

    // 获取封面URL
    const coverUrl = getBestCoverUrl(videoData.cover_image || itemData.cover);

    const result = {
      success: true,
      data: {
        type: 'video',
        // 基本信息
        title: itemData.content || '',
        author: itemData.author?.name || '',
        description: videoData.text || itemData.content || '',
        createTime: itemData.create_time,
        
        // 视频信息
        videoUrl: videoUrl,
        coverUrl: coverUrl,
        duration: itemData.duration || videoData.duration || 0,
        width: videoData.video_width || 0,
        height: videoData.video_height || 0,
        
        // 统计信息
        likeCount: itemData.digg_count || 0,
        commentCount: itemData.comment_count || 0,
        shareCount: itemData.share_count || 0
      }
    };

    if (DEBUG) console.log('✅ 视频解析完成');
    return result;

  } catch (error) {
    return { success: false, message: `视频解析失败: ${error.message}` };
  }
}

/**
 * 解析图文内容
 */
function parseImageContent(itemData) {
  try {
    if (DEBUG) console.log('🖼️ 开始解析图文内容');

    // 获取图片数据 - 直接从cover获取高清无水印图片
    const images = [];
    if (itemData.cover && itemData.cover.url_list && itemData.cover.url_list.length > 0) {
      const imageUrl = getBestImageUrl(itemData.cover);
      if (imageUrl) {
        images.push({
          url: imageUrl,
          width: itemData.cover.width || 0,
          height: itemData.cover.height || 0,
          index: 0
        });
      }
    }

    // 检查是否为纯文本内容（没有图片或封面）
    const isPureText = images.length === 0 && (!itemData.cover || itemData.cover === null);
    
    if (isPureText) {
      if (DEBUG) console.log('📝 检测到纯文本内容');
      
      // 纯文本内容处理
      const content = itemData.content || '';
      const title = content.length > 10 ? content.substring(0, 10) + '...' : content;
      
      const result = {
        success: true,
        data: {
          type: 'text',
          // 基本信息 - 使用内容前10个字作为标题
          title: title,
          author: itemData.author?.name || '',
          description: itemData.content || '',
          createTime: itemData.create_time,
          
          // 文本信息
          content: itemData.content || '',
          
          // 统计信息
          likeCount: itemData.digg_count || 0,
          commentCount: itemData.comment_count || 0,
          shareCount: itemData.share_count || 0
        }
      };

      if (DEBUG) console.log('✅ 纯文本解析完成');
      return result;
    }

    // 有图片的情况，按原逻辑处理
    if (images.length === 0) {
      return { success: false, message: '未找到图片数据' };
    }

    // 获取封面URL
    const coverUrl = getBestCoverUrl(itemData.cover);

    const result = {
      success: true,
      data: {
        type: 'image',
        // 基本信息
        title: itemData.content || '',
        author: itemData.author?.name || '',
        description: itemData.note?.text || itemData.content || '',
        createTime: itemData.create_time,
        
        // 图片信息
        images: images,
        coverUrl: coverUrl,
        imageCount: images.length,
        
        // 统计信息
        likeCount: itemData.digg_count || 0,
        commentCount: itemData.comment_count || 0,
        shareCount: itemData.share_count || 0
      }
    };

    if (DEBUG) console.log('✅ 图文解析完成，图片数量:', images.length);
    return result;

  } catch (error) {
    return { success: false, message: `图文解析失败: ${error.message}` };
  }
}

/**
 * 获取最佳视频URL
 */
function getBestVideoUrl(videoData) {
  try {
    // 优先选择video_high
    if (videoData.video_high && videoData.video_high.url_list && videoData.video_high.url_list.length > 0) {
      return upgradeToHttps(videoData.video_high.url_list[0].url);
    }
    
    // 回退到video_download
    if (videoData.video_download && videoData.video_download.url_list && videoData.video_download.url_list.length > 0) {
      return upgradeToHttps(videoData.video_download.url_list[0].url);
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 获取最佳图片URL
 * 注意：使用url_list获取无水印高清图片，避免使用download_list（有水印）
 */
function getBestImageUrl(imageData) {
  try {
    if (imageData.url_list && imageData.url_list.length > 0) {
      return upgradeToHttps(imageData.url_list[0].url);
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 获取最佳封面URL
 * 注意：使用url_list获取无水印高清封面，避免使用download_list（有水印）
 */
function getBestCoverUrl(coverData) {
  try {
    if (coverData && coverData.url_list && coverData.url_list.length > 0) {
      return upgradeToHttps(coverData.url_list[0].url);
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 将HTTP升级为HTTPS
 */
function upgradeToHttps(url) {
  if (!url || typeof url !== 'string') return '';
  return url.replace(/^http:\/\//, 'https://');
}

