# 微博解析器 (weibo-parser)

## 📖 概述

微博解析器是 MarkEraser 项目的一部分，专门用于解析微博视频链接并提取视频信息。支持多种微博链接格式，能够自动识别和解析微博视频内容。

## 🎯 功能特性

### 支持的链接格式
- ✅ `https://video.weibo.com/show?fid=1034:xxxxx` - 微博视频页面
- ✅ `https://weibo.com/tv/show/xxxxx` - 微博TV页面
- ✅ `https://m.weibo.cn/detail/xxxxx` - 移动端微博详情页
- ✅ `https://weibo.com/xxxxx/status/xxxxx` - 微博状态页面

### 解析能力
- ✅ 自动识别微博链接格式
- ✅ 提取视频标题、作者、描述
- ✅ 获取视频封面图片
- ✅ 提取视频直链地址
- ✅ 支持PC端和移动端解析
- ✅ 多层级数据提取策略

### 技术特性
- ✅ 智能请求头配置
- ✅ 自动重试机制（PC端失败自动切换到移动端）
- ✅ 多种数据提取方法（JSON、Meta标签、页面结构）
- ✅ 完善的错误处理
- ✅ 标准化返回格式

## 🏗️ 架构设计

### 解析策略
1. **PC端优先** - 首先尝试PC端解析，获取更完整的数据
2. **移动端备用** - PC端失败时自动切换到移动端
3. **多层级提取** - JSON数据 > Meta标签 > 页面结构

### 数据提取方法
1. **JSON数据提取** - 查找页面中的初始化数据
2. **Meta标签提取** - 从HTML meta标签获取基本信息
3. **页面结构提取** - 从HTML元素中提取视频信息

## 📁 文件结构

```
weibo-parser/
├── index.js          # 主要解析逻辑
├── package.json      # 项目配置
└── README.md         # 说明文档
```

## 🚀 使用方法

### 1. 直接调用

```javascript
// 调用微博解析器
const result = await uniCloud.callFunction({
  name: 'weibo-parser',
  data: {
    link: 'https://video.weibo.com/show?fid=1034:5197151492309024',
    debug: false
  }
});

if (result.result.success) {
  const data = result.result.data;
  console.log('标题:', data.title);
  console.log('作者:', data.author);
  console.log('视频链接:', data.processedData.videoUrl);
}
```

### 2. 通过统一解析器

```javascript
// 使用统一解析器（自动识别微博链接）
const result = await uniCloud.callFunction({
  name: 'unified-parser',
  data: {
    link: 'https://video.weibo.com/show?fid=1034:5197151492309024'
  }
});
```

## 📊 返回数据格式

### 成功返回
```json
{
  "success": true,
  "data": {
    "title": "视频标题",
    "author": "作者名称",
    "content": "视频描述",
    "coverUrl": "封面图片URL",
    "type": "video",
    "platform": "weibo",
    "source": "微博",
    "originalUrl": "原始链接",
    "processedData": {
      "videoUrl": "视频直链",
      "videoUrls": ["视频链接列表"],
      "duration": null,
      "resolution": null,
      "size": null
    },
    "debug": {
      "parseMethod": "PC端",
      "rawData": "原始页面数据片段"
    }
  }
}
```

### 失败返回
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误堆栈（debug模式下）"
}
```

## ⚙️ 配置选项

### 请求头配置
- **PC端请求头** - 模拟桌面浏览器访问
- **移动端请求头** - 模拟移动设备访问
- **Cookie配置** - 包含必要的认证信息

### 超时设置
- 请求超时：10秒
- 自动重试：PC端失败自动切换到移动端

## 🔧 部署说明

### 1. 在HBuilderX中部署
1. 右键点击 `weibo-parser` 文件夹
2. 选择 "上传并运行"
3. 等待部署完成

### 2. 验证部署
```javascript
// 测试链接
const testLink = 'https://video.weibo.com/show?fid=1034:5197151492309024';
```

## 🧪 测试验证

### 测试用例
1. **标准视频链接** - `https://video.weibo.com/show?fid=1034:xxxxx`
2. **移动端链接** - `https://m.weibo.cn/detail/xxxxx`
3. **TV页面链接** - `https://weibo.com/tv/show/xxxxx`

### 测试要点
- ✅ 链接格式识别
- ✅ 数据提取准确性
- ✅ 错误处理机制
- ✅ 返回格式标准化

## 🐛 常见问题

### 1. 解析失败
- 检查链接格式是否正确
- 确认网络连接正常
- 查看云函数日志获取详细错误信息

### 2. 数据不完整
- 尝试启用debug模式获取更多信息
- 检查页面结构是否发生变化
- 考虑更新解析策略

### 3. 视频链接无效
- 微博视频可能有访问限制
- 需要登录或特定权限
- 视频可能已被删除

## 🔮 后续优化

### 功能增强
- [ ] 支持更多微博内容类型
- [ ] 添加视频质量选择
- [ ] 优化解析性能
- [ ] 增加缓存机制

### 监控维护
- [ ] 添加使用统计
- [ ] 监控解析成功率
- [ ] 定期更新解析逻辑
- [ ] 自动化测试

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 初始版本发布
- ✅ 支持基本微博视频解析
- ✅ 集成统一解析器架构
- ✅ 完善的错误处理

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进微博解析器！

## 📄 许可证

MIT License - 详见 [LICENSE](../LICENSE) 文件

---

**开发团队**: MarkEraser Team  
**最后更新**: 2024年1月  
**版本**: v1.0.0


