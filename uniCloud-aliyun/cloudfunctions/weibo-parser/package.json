{"name": "weibo-parser", "version": "1.0.0", "description": "微博视频解析器 - 支持微博视频链接解析和去水印处理", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["weibo", "video", "parser", "watermark-remover", "uniCloud"], "author": "MarkEraser Team", "license": "MIT", "dependencies": {}, "devDependencies": {}, "uniCloud": {"type": "cloudfunction", "platform": "<PERSON><PERSON><PERSON>"}}