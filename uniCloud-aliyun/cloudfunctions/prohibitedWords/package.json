{"name": "prohibitedwords", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "cloudfunction-config": {"memorySize": 256, "timeout": 5, "triggers": [{"name": "myTrigger", "type": "timer", "config": "0 0 2 1 * * *"}], "path": "/http/prohibited-words"}, "origin-plugin-dev-name": "uni-cloud", "origin-plugin-version": "1.4.2", "plugin-dev-name": "uni-cloud", "plugin-version": "1.4.2"}