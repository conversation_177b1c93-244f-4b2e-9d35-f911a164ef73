// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": "'add_view_count' in action",
		"create": "'add_view_count' in action",
		"update":"'add_view_count' in action",
		"delete": "'add_view_count' in action"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"state":{
			"bsonType":"int",
			"forceDefaultValue":0
		},
		// "uid":{
		// 	"forceDefaultValue":{
		// 		"$env":"uid"
		// 	}
		// },
		"create_time":{
			"forceDefaultValue":{
				"$env":"now"
			}
		},
		"text":{
			"bsonType":"string",
			"defaultValue":"该用户比较懒啥也没写！"
		}
	}
}
