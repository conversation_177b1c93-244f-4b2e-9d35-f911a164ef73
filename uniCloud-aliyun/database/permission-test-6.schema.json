// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": "'AUDITOR' in auth.role",
		"create": "'AUDITOR' in auth.role",
		"update":"'AUDITOR' in auth.role",
		"delete": "'AUDITOR' in auth.role"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"state":{
			"bsonType":"int",
			"forceDefaultValue":0
		},
		"uid":{
			"forceDefaultValue":{
				"$env":"uid"
			}
		},
		"create_time":{
			"forceDefaultValue":{
				"$env":"now"
			}
		},
		"text":{
			"bsonType":"string",
			"defaultValue":"该用户比较懒啥也没写！"
		}
	}
}
