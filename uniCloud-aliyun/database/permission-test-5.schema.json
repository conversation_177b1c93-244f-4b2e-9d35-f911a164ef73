// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": "doc.create_time > (now - 60000)", //表示最近1分钟内的数据
		"create": true,
		"update":"doc.create_time > (now - 60000)",
		"delete": "doc.create_time > (now - 60000)"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"state":{
			"bsonType":"int",
			"forceDefaultValue":0
		},
		"uid":{
			"forceDefaultValue":{
				"$env":"uid"
			}
		},
		"create_time":{
			"defaultValue":{
				"$env":"now"
			}
		},
		"text":{
			"bsonType":"string",
			"defaultValue":"该用户比较懒啥也没写！"
		}
	}
}
