// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
  "bsonType": "object",
  "required": [],
  "permission": {
    "read": "action in 'add_view_count'",
    "create": false,
    "update": false,
    "delete": false
  },
  "properties": {
    "_id": {
      "description": "ID，系统自动生成",
      "foreignKey": "comment.notice_id"
    },
    "data": {
      "bsonType": "string",
      "description": "公告内容",
      "label": "公告内容",
      "maxLength": 32,
      "errorMessage": {
        "maxLength": "{label}不能大于{maxLength}个字符"
      }
    },
    "view_count": {
      "bsonType": "int",
      "title": "阅读数量",
      "description": "阅读数量",
      "permission": {
        "write": false
      }
    },
    "create_time": {
      "description": "创建时间",
      "bsonType": "timestamp",
      "forceDefaultValue": {
        "$env": "now"
      }
    },
    "update_time": {
      "description": "更新时间",
      "bsonType": "timestamp",
      "forceDefaultValue": {
        "$env": "now"
      }
    }
  }
}