// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": false,
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
    "product": {
    	"description": "产品名称",
      "bsonType": "string"
    },
    "create_time": {
    	"description": "创建时间",
      "bsonType": "timestamp",
      "defaultValue":{
        "$env": "now"
      }
    }
	}
}