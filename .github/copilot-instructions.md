# MarkEraser AI Agent Instructions

## 项目概述

MarkEraser是一个基于uni-app和uniCloud开发的视频去水印工具应用。主要提供视频水印去除服务，支持多平台视频链接解析。

### 技术栈
- 前端框架: uni-app (Vue2/Vue3)
- 云服务: uniCloud (阿里云)
- UI组件: uni-ui

## 核心功能模块

### 1. 水印去除功能 (`pages/watermark-remover/`)
- 主页面: `pages/watermark-remover/index.vue`
- 支持多平台链接解析(抖音、快手、小红书等)
- 使用云函数处理视频下载和水印去除

### 2. 历史记录管理 (`pages/history/`)
- 本地存储用户处理记录
- 支持清空、单条删除、重新处理等操作
- 使用 `uni.getStorageSync/setStorageSync` 管理数据

### 3. 用户中心 (`pages/profile/`)
- 用户信息管理
- 应用设置
- 使用状态统计

## 重要工作流程

### 1. 视频去水印流程
```js
// 1. 用户输入视频链接
// 2. 清理并验证链接格式
cleanLink(link) {
  link = link.trim().replace(/\s+/g, ' ')
  const urlMatch = link.match(/(https?:\/\/[^\s]+)/)
  return urlMatch ? urlMatch[1] : link
}

// 3. 调用云函数处理
await uniCloud.callFunction({
  name: 'simple-douyin-parser',
  data: {
    link: cleanedLink,
    forceRemoveWatermark: true
  }
})

// 4. 保存历史记录
saveToHistory(data) {
  const history = uni.getStorageSync('watermark_history') || []
  history.unshift({...data, timestamp: Date.now()})
}
```

### 2. 历史记录管理
- 使用 `watermark_history` 作为存储键
- 记录包含: 标题、作者、来源平台、处理时间、下载链接等信息
- 限制最大记录数为50条

## 开发规范

### 1. 目录结构
```
MarkEraser/
├── pages/                    # 页面文件
├── components/              # 公共组件
├── uniCloud-aliyun/         # 云函数与数据库
└── static/                  # 静态资源
```

### 2. 命名规范
- 组件文件: 使用kebab-case (如: `page-head.vue`)
- 页面文件: 使用kebab-case (如: `watermark-remover.vue`)
- 方法名: 使用camelCase (如: `cleanLink`)
- 存储键: 使用snake_case (如: `watermark_history`)

### 3. API调用规范
- 统一使用 uni-app API
- 云函数调用使用 try-catch 包裹
- 状态管理优先使用本地存储

## 常用开发任务

### 本地开发
1. 使用 HBuilderX 打开项目
2. 运行方式:
   - 微信小程序: 运行 -> 运行到小程序模拟器
   - H5: 运行 -> 运行到浏览器
   - App: 运行 -> 运行到手机或模拟器

### 云函数开发
1. 在 `uniCloud-aliyun/cloudfunctions/` 下创建云函数
2. 本地测试: 在HBuilderX中右键云函数 -> "本地运行云函数"
3. 部署: 右键云函数 -> "上传部署"

### 注意事项
- 确保云函数修改后及时上传部署
- 本地存储的数据结构变更需要考虑兼容性
- 视频处理相关功能需要在云函数中实现
